/* 演示容器 */
.presentationContainer {
  width: 100vw;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow-x: hidden; /* 只隐藏横向滚动条 */
  overflow-y: auto; /* 允许垂直滚动 */
}

.presentationContainer.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  overflow: hidden; /* 全屏模式下完全隐藏滚动条 */
}

/* 幻灯片容器 */
.slideContainer {
  width: 100%;
  min-height: 100vh;
  position: relative;
}

/* 单个幻灯片 */
.slide {
  width: 100%;
  min-height: 100vh;
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  align-items: flex-start; /* 改为顶部对齐，支持内容超出时的滚动 */
  justify-content: center;
  opacity: 0;
  transform: translateX(100px);
  transition: all 0.5s ease-in-out;
  padding: 2rem 1rem;
  box-sizing: border-box;
}

.slide.active {
  opacity: 1;
  transform: translateX(0);
}

/* 幻灯片内容 */
.slideContent {
  max-width: 1200px;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 3rem 3rem 5rem 3rem; /* 恢复正常内边距，为导航留出空间 */
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  text-align: center;
  margin: auto; /* 水平居中 */
}

/* 标题样式 */
.mainTitle {
  font-size: 3.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
}

.slideTitle {
  font-size: 2.5rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 2rem;
  text-align: center;
  line-height: 1.3;
}

.subtitle {
  font-size: 1.5rem;
  color: #7f8c8d;
  margin-bottom: 2rem;
  line-height: 1.4;
}

/* 装饰元素 */
.decorativeElements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.circle, .triangle, .square {
  position: absolute;
  opacity: 0.1;
}

.circle {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: #3498db;
  top: 10%;
  right: 10%;
  animation: float 6s ease-in-out infinite;
}

.triangle {
  width: 0;
  height: 0;
  border-left: 100px solid transparent;
  border-right: 100px solid transparent;
  border-bottom: 173px solid #e74c3c;
  bottom: 15%;
  left: 15%;
  animation: float 8s ease-in-out infinite reverse;
}

.square {
  width: 150px;
  height: 150px;
  background: #f39c12;
  transform: rotate(45deg);
  top: 60%;
  right: 20%;
  animation: float 7s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

/* 大纲网格 */
.outlineGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  text-align: left;
}

.outlineSection {
  background: rgba(255, 255, 255, 0.8);
  padding: 1.5rem;
  border-radius: 15px;
  border-left: 4px solid #3498db;
}

.outlineSection h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.outlineSection ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.outlineSection li {
  padding: 0.3rem 0;
  color: #34495e;
  position: relative;
  padding-left: 1.5rem;
  font-size: 1rem;
}

.outlineSection li::before {
  content: "▸";
  position: absolute;
  left: 0;
  color: #3498db;
  font-weight: bold;
}

/* 内容网格 */
.contentGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  text-align: left;
}

/* 概念卡片 */
.conceptCard {
  background: rgba(255, 255, 255, 0.9);
  padding: 2rem;
  border-radius: 15px;
  border: 2px solid #ecf0f1;
  transition: transform 0.3s ease;
}

.conceptCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.conceptCard h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.4rem;
  border-bottom: 2px solid #3498db;
  padding-bottom: 0.5rem;
}

/* 比较样式 */
.comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.comparisonItem {
  padding: 1rem;
  background: rgba(52, 152, 219, 0.1);
  border-radius: 10px;
  border-left: 3px solid #3498db;
}

.comparisonItem strong {
  display: block;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

/* 项目列表 */
.bulletList {
  list-style: none;
  padding: 0;
}

.bulletList li {
  padding: 0.5rem 0;
  position: relative;
  padding-left: 2rem;
  color: #34495e;
}

.bulletList li::before {
  content: "●";
  position: absolute;
  left: 0;
  color: #3498db;
  font-size: 1.2rem;
}

/* JTB组件样式 */
.jtbContainer {
  text-align: center;
}

.jtbDefinition {
  background: rgba(52, 152, 219, 0.1);
  padding: 1.5rem;
  border-radius: 10px;
  margin-bottom: 2rem;
  font-size: 1.2rem;
  color: #2c3e50;
}

.jtbComponents {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin-bottom: 2rem;
}

.jtbComponent {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.jtbComponent:hover {
  transform: translateY(-5px);
}

.componentIcon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: bold;
  margin: 0 auto 1rem;
}

.jtbComponent h3 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.jtbExample {
  background: rgba(241, 196, 15, 0.1);
  padding: 1.5rem;
  border-radius: 10px;
  border-left: 4px solid #f1c40f;
  text-align: left;
}

.jtbExample h4 {
  color: #2c3e50;
  margin-bottom: 1rem;
}

/* 盖梯尔问题样式 */
.gettierContainer {
  text-align: left;
}

.gettierProblem {
  background: rgba(231, 76, 60, 0.1);
  padding: 1.5rem;
  border-radius: 10px;
  border-left: 4px solid #e74c3c;
  margin-bottom: 2rem;
  text-align: center;
}

.gettierProblem h3 {
  color: #c0392b;
  margin-bottom: 1rem;
}

.gettierExample h4 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  text-align: center;
}

.exampleSteps {
  display: grid;
  gap: 1rem;
}

.step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 10px;
}

.stepNumber {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #3498db;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  flex-shrink: 0;
}

.gettierImplication {
  background: rgba(155, 89, 182, 0.1);
  padding: 1.5rem;
  border-radius: 10px;
  border-left: 4px solid #9b59b6;
  margin-top: 2rem;
}

.gettierImplication h4 {
  color: #8e44ad;
  margin-bottom: 1rem;
}

/* 传统对比样式 */
.traditionsContainer {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.tradition {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.tradition h3 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  text-align: center;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #3498db;
}

.traditionContent h4 {
  color: #34495e;
  margin: 1rem 0 0.5rem;
  font-size: 1.1rem;
}

.traditionContent ul {
  list-style: none;
  padding: 0;
  margin-bottom: 1rem;
}

.traditionContent li {
  padding: 0.3rem 0;
  position: relative;
  padding-left: 1.5rem;
  color: #7f8c8d;
}

.traditionContent li::before {
  content: "▸";
  position: absolute;
  left: 0;
  color: #3498db;
}

.traditionContent p {
  color: #7f8c8d;
  margin-bottom: 1rem;
}

/* 导航和控制 */
.navigation {
  position: fixed;
  bottom: 1rem;
  right: 2rem;
  display: flex;
  align-items: center;
  gap: 0.8rem;
  background: rgba(255, 255, 255, 0.98);
  padding: 0.8rem 1.5rem;
  border-radius: 30px;
  backdrop-filter: blur(15px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  z-index: 1000;
}

.navButton {
  background: #3498db;
  color: white;
  border: none;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  min-width: 60px;
}

.navButton:hover:not(:disabled) {
  background: #2980b9;
  transform: translateY(-1px);
}

.navButton:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
  opacity: 0.6;
}

.slideCounter {
  color: #2c3e50;
  font-weight: 600;
  min-width: 60px;
  text-align: center;
  font-size: 0.9rem;
}

.controls {
  position: fixed;
  top: 2rem;
  left: 2rem;
  z-index: 1000;
}

.fullscreenButton {
  background: rgba(255, 255, 255, 0.95);
  border: none;
  padding: 0.6rem 1.2rem;
  border-radius: 25px;
  cursor: pointer;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  font-weight: 500;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.fullscreenButton:hover {
  background: rgba(255, 255, 255, 1);
  transform: translateY(-1px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.instructions {
  position: fixed;
  top: 2rem;
  right: 2rem;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.6rem 1.2rem;
  border-radius: 25px;
  font-size: 0.85rem;
  backdrop-filter: blur(10px);
  z-index: 1000;
}

/* AI定义样式 */
.aiDefinitionContainer {
  text-align: left;
}

.definitionCard {
  background: rgba(52, 152, 219, 0.1);
  padding: 2rem;
  border-radius: 15px;
  border-left: 4px solid #3498db;
  margin-bottom: 2rem;
}

.definitionCard h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.4rem;
}

.aiGoals {
  margin-bottom: 2rem;
}

.aiGoals h3 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  text-align: center;
}

.goalsGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
}

.goalItem {
  background: white;
  padding: 1.5rem;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.goalItem:hover {
  transform: translateY(-5px);
}

.goalIcon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.goalItem h4 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.goalItem p {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.aiDuality h3 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  text-align: center;
}

.dualityComparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.dualityItem {
  background: rgba(155, 89, 182, 0.1);
  padding: 1.5rem;
  border-radius: 10px;
  border-left: 3px solid #9b59b6;
}

.dualityItem strong {
  display: block;
  color: #8e44ad;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

/* AI范式样式 */
.paradigmsContainer {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.paradigm {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.paradigm h3 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  text-align: center;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #3498db;
  font-size: 1.3rem;
}

.paradigmContent h4 {
  color: #34495e;
  margin: 1.5rem 0 0.8rem;
  font-size: 1.1rem;
}

.paradigmContent ul {
  list-style: none;
  padding: 0;
  margin-bottom: 1rem;
}

.paradigmContent li {
  padding: 0.4rem 0;
  position: relative;
  padding-left: 1.5rem;
  color: #7f8c8d;
}

.paradigmContent li::before {
  content: "▸";
  position: absolute;
  left: 0;
  color: #3498db;
}

/* JTB挑战样式 */
.challengeContainer {
  text-align: center;
}

.challengeQuestion {
  background: rgba(231, 76, 60, 0.1);
  padding: 2rem;
  border-radius: 15px;
  border-left: 4px solid #e74c3c;
  margin-bottom: 2rem;
}

.challengeQuestion h3 {
  color: #c0392b;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.jtbChallenges {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

.challengeItem {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  text-align: left;
}

.challengeIcon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.3rem;
  font-weight: bold;
  margin: 0 auto 1rem;
}

.challengeItem h4 {
  color: #2c3e50;
  margin-bottom: 1rem;
  text-align: center;
}

.challengeItem ul {
  list-style: none;
  padding: 0;
}

.challengeItem li {
  padding: 0.4rem 0;
  position: relative;
  padding-left: 1.5rem;
  color: #7f8c8d;
}

.challengeItem li::before {
  content: "×";
  position: absolute;
  left: 0;
  color: #e74c3c;
  font-weight: bold;
}

/* LLM样式 */
.llmContainer {
  text-align: left;
}

.llmDebate {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.debatePosition {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.debatePosition h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
  text-align: center;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #3498db;
}

.debatePosition ul {
  list-style: none;
  padding: 0;
}

.debatePosition li {
  padding: 0.5rem 0;
  position: relative;
  padding-left: 1.5rem;
  color: #7f8c8d;
}

.debatePosition li::before {
  content: "▸";
  position: absolute;
  left: 0;
  color: #3498db;
}

.llmRisk {
  background: rgba(241, 196, 15, 0.1);
  padding: 1.5rem;
  border-radius: 10px;
  border-left: 4px solid #f1c40f;
  margin-bottom: 2rem;
}

.llmRisk h3 {
  color: #f39c12;
  margin-bottom: 1rem;
}

.philosophicalQuestion {
  background: rgba(155, 89, 182, 0.1);
  padding: 1.5rem;
  border-radius: 10px;
  border-left: 4px solid #9b59b6;
}

.philosophicalQuestion h3 {
  color: #8e44ad;
  margin-bottom: 1rem;
}

/* 中文屋样式 */
.chineseRoomContainer {
  text-align: left;
}

.argumentDescription {
  margin-bottom: 2rem;
}

.argumentDescription h3 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  text-align: center;
}

.experimentSteps {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
}

.experimentStep {
  background: white;
  padding: 1.5rem;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.experimentStep:hover {
  transform: translateY(-5px);
}

.stepIcon {
  font-size: 2rem;
  margin-bottom: 1rem;
  display: block;
}

.experimentStep p {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.argumentCore {
  background: rgba(52, 152, 219, 0.1);
  padding: 2rem;
  border-radius: 15px;
  border-left: 4px solid #3498db;
  margin-bottom: 2rem;
  text-align: center;
}

.argumentCore h3 {
  color: #2980b9;
  margin-bottom: 1rem;
}

.modernRelevance h3 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  text-align: center;
}

.relevancePoints {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
}

.relevancePoint {
  background: white;
  padding: 1.5rem;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.relevancePoint h4 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.relevancePoint p {
  color: #7f8c8d;
  font-size: 0.9rem;
}

/* 黑箱问题样式 */
.blackBoxContainer {
  text-align: left;
}

.problemDescription {
  background: rgba(52, 73, 94, 0.1);
  padding: 2rem;
  border-radius: 15px;
  border-left: 4px solid #34495e;
  margin-bottom: 2rem;
  text-align: center;
}

.problemDescription h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.4rem;
}

.blackBoxChallenges {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.challengeCard {
  background: white;
  padding: 1.5rem;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.challengeCard:hover {
  transform: translateY(-5px);
}

.challengeCard h4 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.challengeCard p {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.solutionDirection {
  background: rgba(46, 204, 113, 0.1);
  padding: 1.5rem;
  border-radius: 10px;
  border-left: 4px solid #2ecc71;
}

.solutionDirection h4 {
  color: #27ae60;
  margin-bottom: 1rem;
}

.solutionDirection ul {
  list-style: none;
  padding: 0;
}

.solutionDirection li {
  padding: 0.3rem 0;
  position: relative;
  padding-left: 1.5rem;
  color: #7f8c8d;
}

.solutionDirection li::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #2ecc71;
  font-weight: bold;
}

/* 算法偏见样式 */
.biasContainer {
  text-align: left;
}

.biasDefinition {
  background: rgba(231, 76, 60, 0.1);
  padding: 2rem;
  border-radius: 15px;
  border-left: 4px solid #e74c3c;
  margin-bottom: 2rem;
  text-align: center;
}

.biasDefinition h3 {
  color: #c0392b;
  margin-bottom: 1rem;
  font-size: 1.4rem;
}

.biasProcess {
  margin-bottom: 2rem;
}

.biasProcess h4 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  text-align: center;
}

.processSteps {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
}

.processStep {
  background: white;
  padding: 1.5rem;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s ease;
}

.processStep:hover {
  transform: translateY(-5px);
}

.processStep .stepNumber {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin: 0 auto 1rem;
}

.processStep h5 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

.processStep p {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.epistemicInjustice {
  background: rgba(155, 89, 182, 0.1);
  padding: 1.5rem;
  border-radius: 10px;
  border-left: 4px solid #9b59b6;
  margin-bottom: 2rem;
}

.epistemicInjustice h4 {
  color: #8e44ad;
  margin-bottom: 1rem;
}

.biasResolution {
  background: rgba(46, 204, 113, 0.1);
  padding: 1.5rem;
  border-radius: 10px;
  border-left: 4px solid #2ecc71;
}

.biasResolution h4 {
  color: #27ae60;
  margin-bottom: 1rem;
}

.biasResolution ul {
  list-style: none;
  padding: 0;
}

.biasResolution li {
  padding: 0.4rem 0;
  position: relative;
  padding-left: 1.5rem;
  color: #7f8c8d;
}

.biasResolution li::before {
  content: "▸";
  position: absolute;
  left: 0;
  color: #2ecc71;
}

/* AI幻觉样式 */
.hallucinationContainer {
  text-align: left;
}

.hallucinationDefinition {
  background: rgba(241, 196, 15, 0.1);
  padding: 2rem;
  border-radius: 15px;
  border-left: 4px solid #f1c40f;
  margin-bottom: 2rem;
  text-align: center;
}

.hallucinationDefinition h3 {
  color: #f39c12;
  margin-bottom: 1rem;
  font-size: 1.4rem;
}

.hallucinationCauses {
  margin-bottom: 2rem;
}

.hallucinationCauses h4 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  text-align: center;
}

.causesGrid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
}

.causeItem {
  background: white;
  padding: 1.5rem;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s ease;
}

.causeItem:hover {
  transform: translateY(-5px);
}

.causeIcon {
  font-size: 2rem;
  margin-bottom: 1rem;
  display: block;
}

.causeItem h5 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

.causeItem p {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.epistemicThreat {
  background: rgba(231, 76, 60, 0.1);
  padding: 1.5rem;
  border-radius: 10px;
  border-left: 4px solid #e74c3c;
  margin-bottom: 2rem;
  text-align: center;
}

.epistemicThreat h3 {
  color: #c0392b;
  margin-bottom: 1rem;
}

.verificationImportance {
  background: rgba(46, 204, 113, 0.1);
  padding: 1.5rem;
  border-radius: 10px;
  border-left: 4px solid #2ecc71;
}

.verificationImportance h4 {
  color: #27ae60;
  margin-bottom: 1rem;
}

.verificationImportance ul {
  list-style: none;
  padding: 0;
}

.verificationImportance li {
  padding: 0.4rem 0;
  position: relative;
  padding-left: 1.5rem;
  color: #7f8c8d;
}

.verificationImportance li::before {
  content: "!";
  position: absolute;
  left: 0;
  color: #f39c12;
  font-weight: bold;
}

/* 神经符号AI样式 */
.neuroSymbolicContainer {
  text-align: left;
}

.integrationVision {
  background: rgba(52, 152, 219, 0.1);
  padding: 2rem;
  border-radius: 15px;
  border-left: 4px solid #3498db;
  margin-bottom: 2rem;
  text-align: center;
}

.integrationVision h3 {
  color: #2980b9;
  margin-bottom: 1rem;
  font-size: 1.4rem;
}

.integrationApproaches {
  margin-bottom: 2rem;
}

.integrationApproaches h4 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  text-align: center;
}

.approachesGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
}

.approachItem {
  background: white;
  padding: 1.5rem;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s ease;
}

.approachItem:hover {
  transform: translateY(-5px);
}

.approachItem h5 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.approachItem p {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.epistemicAdvantages {
  margin-bottom: 2rem;
}

.epistemicAdvantages h4 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  text-align: center;
}

.advantagesGrid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
}

.advantageItem {
  background: white;
  padding: 1.5rem;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s ease;
}

.advantageItem:hover {
  transform: translateY(-5px);
}

.advantageIcon {
  font-size: 2rem;
  margin-bottom: 1rem;
  display: block;
}

.advantageItem h5 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

.advantageItem p {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.philosophicalSignificance {
  background: rgba(155, 89, 182, 0.1);
  padding: 1.5rem;
  border-radius: 10px;
  border-left: 4px solid #9b59b6;
}

.philosophicalSignificance h4 {
  color: #8e44ad;
  margin-bottom: 1rem;
}

/* 人机认知增强样式 */
.augmentationContainer {
  text-align: left;
}

.extendedMind {
  background: rgba(52, 152, 219, 0.1);
  padding: 2rem;
  border-radius: 15px;
  border-left: 4px solid #3498db;
  margin-bottom: 2rem;
  text-align: center;
}

.extendedMind h3 {
  color: #2980b9;
  margin-bottom: 1rem;
  font-size: 1.4rem;
}

.cognitiveOffloading {
  margin-bottom: 2rem;
}

.cognitiveOffloading h3 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  text-align: center;
}

.offloadingExamples {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
}

.offloadingItem {
  background: white;
  padding: 1.5rem;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s ease;
}

.offloadingItem:hover {
  transform: translateY(-5px);
}

.offloadingIcon {
  font-size: 2rem;
  margin-bottom: 1rem;
  display: block;
}

.offloadingItem h4 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

.offloadingItem p {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.epistemicChallenges {
  margin-bottom: 2rem;
}

.epistemicChallenges h3 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  text-align: center;
}

.challengesGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
}

.challengeBox {
  background: rgba(231, 76, 60, 0.1);
  padding: 1.5rem;
  border-radius: 15px;
  border-left: 4px solid #e74c3c;
}

.challengeBox h4 {
  color: #c0392b;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.challengeBox p {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.balanceStrategy {
  background: rgba(46, 204, 113, 0.1);
  padding: 1.5rem;
  border-radius: 10px;
  border-left: 4px solid #2ecc71;
}

.balanceStrategy h4 {
  color: #27ae60;
  margin-bottom: 1rem;
}

/* 科学教育样式 */
.scienceEducationContainer {
  text-align: left;
}

.scienceSection,
.educationSection {
  margin-bottom: 2rem;
}

.scienceSection h3,
.educationSection h3 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  text-align: center;
  font-size: 1.4rem;
}

.scienceContent,
.educationContent {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.scienceItem,
.educationItem,
.scienceChallenge,
.educationChallenge {
  background: white;
  padding: 1.5rem;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.scienceItem h4,
.educationItem h4 {
  color: #3498db;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.scienceChallenge h4,
.educationChallenge h4 {
  color: #e74c3c;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.scienceItem ul,
.educationItem ul,
.scienceChallenge ul,
.educationChallenge ul {
  list-style: none;
  padding: 0;
}

.scienceItem li,
.educationItem li {
  padding: 0.3rem 0;
  position: relative;
  padding-left: 1.5rem;
  color: #7f8c8d;
}

.scienceItem li::before,
.educationItem li::before {
  content: "▸";
  position: absolute;
  left: 0;
  color: #3498db;
}

.scienceChallenge li,
.educationChallenge li {
  padding: 0.3rem 0;
  position: relative;
  padding-left: 1.5rem;
  color: #7f8c8d;
}

.scienceChallenge li::before,
.educationChallenge li::before {
  content: "⚠";
  position: absolute;
  left: 0;
  color: #e74c3c;
}

.newFoundation {
  background: rgba(155, 89, 182, 0.1);
  padding: 2rem;
  border-radius: 15px;
  border-left: 4px solid #9b59b6;
  text-align: center;
}

.newFoundation h3 {
  color: #8e44ad;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

/* 总结样式 */
.summaryContainer {
  text-align: left;
}

.keyInsights {
  margin-bottom: 2rem;
}

.keyInsights h3 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  text-align: center;
  font-size: 1.4rem;
}

.insightsGrid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
}

.insightItem {
  background: white;
  padding: 1.5rem;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s ease;
}

.insightItem:hover {
  transform: translateY(-5px);
}

.insightIcon {
  font-size: 2rem;
  margin-bottom: 1rem;
  display: block;
}

.insightItem h4 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

.insightItem p {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.futureDirections {
  margin-bottom: 2rem;
}

.futureDirections h3 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  text-align: center;
  font-size: 1.4rem;
}

.directionsGrid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
}

.directionItem {
  background: white;
  padding: 1.5rem;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.directionItem h4 {
  color: #3498db;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.directionItem ul {
  list-style: none;
  padding: 0;
}

.directionItem li {
  padding: 0.3rem 0;
  position: relative;
  padding-left: 1.5rem;
  color: #7f8c8d;
  font-size: 0.9rem;
}

.directionItem li::before {
  content: "▸";
  position: absolute;
  left: 0;
  color: #3498db;
}

.finalThought {
  background: linear-gradient(135deg, rgba(52, 152, 219, 0.1), rgba(155, 89, 182, 0.1));
  padding: 2rem;
  border-radius: 15px;
  border-left: 4px solid #3498db;
  text-align: center;
}

.finalThought h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.finalThought p {
  color: #34495e;
  font-size: 1.1rem;
  line-height: 1.6;
  font-style: italic;
}

/* 致谢样式 */
.thankYouTitle {
  font-size: 4rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 2rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.thankYouContent {
  text-align: center;
}

.thankYouSubtitle {
  font-size: 1.8rem;
  color: #7f8c8d;
  margin-bottom: 3rem;
}

.contactInfo {
  background: rgba(255, 255, 255, 0.9);
  padding: 2rem;
  border-radius: 15px;
  margin-bottom: 2rem;
  text-align: left;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.contactInfo h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
  text-align: center;
}

.contactInfo p {
  color: #34495e;
  margin-bottom: 1rem;
  text-align: center;
}

.contactInfo ul {
  list-style: none;
  padding: 0;
}

.contactInfo li {
  padding: 0.5rem 0;
  position: relative;
  padding-left: 2rem;
  color: #7f8c8d;
}

.contactInfo li::before {
  content: "💭";
  position: absolute;
  left: 0;
  font-size: 1.2rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .slide {
    padding: 0.5rem;
  }

  .slideContent {
    padding: 1.5rem 1.5rem 3rem 1.5rem;
    max-height: 95vh;
    overflow: hidden;
  }

  .mainTitle {
    font-size: 2.2rem;
    margin-bottom: 0.8rem;
  }

  .slideTitle {
    font-size: 1.8rem;
    margin-bottom: 1rem;
  }

  .subtitle {
    font-size: 1.1rem;
    margin-bottom: 1rem;
  }

  .thankYouTitle {
    font-size: 2.5rem;
  }

  .outlineGrid,
  .contentGrid,
  .jtbComponents,
  .traditionsContainer,
  .paradigmsContainer,
  .jtbChallenges,
  .llmDebate,
  .relevancePoints,
  .blackBoxChallenges,
  .processSteps,
  .causesGrid,
  .approachesGrid,
  .advantagesGrid,
  .offloadingExamples,
  .challengesGrid,
  .scienceContent,
  .educationContent,
  .insightsGrid,
  .directionsGrid {
    grid-template-columns: 1fr;
  }

  .comparison,
  .dualityComparison {
    grid-template-columns: 1fr;
  }

  .goalsGrid {
    grid-template-columns: 1fr;
  }

  .experimentSteps {
    grid-template-columns: 1fr;
  }

  .navigation {
    bottom: 0.5rem;
    right: 1rem;
    padding: 0.6rem 1rem;
    gap: 0.5rem;
  }

  .navButton {
    padding: 0.3rem 0.6rem;
    font-size: 0.8rem;
    min-width: 50px;
  }

  .slideCounter {
    font-size: 0.8rem;
    min-width: 50px;
  }

  .controls {
    top: 1rem;
    left: 1rem;
  }

  .fullscreenButton {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }

  .instructions {
    top: 1rem;
    right: 1rem;
    padding: 0.4rem 0.8rem;
    font-size: 0.75rem;
  }

  .contactInfo {
    margin: 0 1rem 2rem;
  }
}
