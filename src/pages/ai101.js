import { useState, useEffect, useRef, useCallback, forwardRef } from 'react';
import Layout from '@theme/Layout';
import Translate, { translate } from '@docusaurus/Translate';
import Heading from '@theme/Heading';
import styles from './ai101.module.css';

// Cover slide component
const CoverSlide = forwardRef((props, ref) => {
  return (
    <section className={styles.slide} ref={ref}>
      <div className={styles.slideContent}>
        <Heading as="h1" className={styles.slideTitle}>
          <Translate id="ai101.title">
            Opportunities and Challenges in the AI Era
          </Translate>
        </Heading>
        <div className={styles.decorativeShape + ' ' + styles.circle} style={{ top: '10%', left: '10%' }}></div>
        <div className={styles.decorativeShape + ' ' + styles.triangle} style={{ bottom: '10%', right: '10%' }}></div>
      </div>
    </section>
  );
});

// AI History slide
const AIHistorySlide = forwardRef((props, ref) => {
  return (
    <section className={styles.slide} ref={ref}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.slide1.title">
            A Brief History of AI Development
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.slide1.subtitle">
            70 Years Journey from Concept to Reality
          </Translate>
        </p>

        <div className={styles.contentSection}>
          <div className={styles.timeline}>
            <div className={styles.timelineItem}>
              <div className={styles.timelineYear}>1950</div>
              <div className={styles.timelineEvent}>
                <Translate id="ai101.timeline.1950">
                  Turing proposed the Turing Test
                </Translate>
              </div>
            </div>
            <div className={styles.timelineItem}>
              <div className={styles.timelineYear}>1956</div>
              <div className={styles.timelineEvent}>
                <Translate id="ai101.timeline.1956">
                  Dartmouth Conference, birth of AI concept
                </Translate>
              </div>
            </div>
            <div className={styles.timelineItem}>
              <div className={styles.timelineYear}>1980-1990s</div>
              <div className={styles.timelineEvent}>
                <Translate id="ai101.timeline.1980s">
                  Rise of expert systems and AI winter
                </Translate>
              </div>
            </div>
            <div className={styles.timelineItem}>
              <div className={styles.timelineYear}>2000-2010s</div>
              <div className={styles.timelineEvent}>
                <Translate id="ai101.timeline.2000s">
                  Machine learning renaissance
                </Translate>
              </div>
            </div>
            <div className={styles.timelineItem}>
              <div className={styles.timelineYear}>2012</div>
              <div className={styles.timelineEvent}>
                <Translate id="ai101.timeline.2012">
                  Deep learning breakthrough (AlexNet)
                </Translate>
              </div>
            </div>
            <div className={styles.timelineItem}>
              <div className={styles.timelineYear}>2017</div>
              <div className={styles.timelineEvent}>
                <Translate id="ai101.timeline.2017">
                  Transformer architecture emerged
                </Translate>
              </div>
            </div>
            <div className={styles.timelineItem}>
              <div className={styles.timelineYear}>2022</div>
              <div className={styles.timelineEvent}>
                <Translate id="ai101.timeline.2022">
                  ChatGPT sparked generative AI revolution
                </Translate>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
});

// Why Now slide
function WhyNowSlide({ ref }) {
  return (
    <section className={styles.slide} id="slide-2" ref={ref}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.slide2.title">
            Why Now?
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.slide2.subtitle">
            Perfect Convergence of Three Key Elements
          </Translate>
        </p>

        <div className={styles.contentGrid}>
          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🔧</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.whyNow.computing.title">
                Computing Power Breakthrough
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <ul className={styles.bulletList}>
                <li>
                  <Translate id="ai101.whyNow.computing.item1">
                    GPU parallel computing revolution
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.whyNow.computing.item2">
                    Cloud computing lowering barriers
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.whyNow.computing.item3">
                    Specialized AI chips emerging
                  </Translate>
                </li>
              </ul>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>📊</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.whyNow.data.title">
                Data Explosion
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <ul className={styles.bulletList}>
                <li>
                  <Translate id="ai101.whyNow.data.item1">
                    Internet content exponential growth
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.whyNow.data.item2">
                    Accelerated digitization process
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.whyNow.data.item3">
                    Mature data annotation techniques
                  </Translate>
                </li>
              </ul>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🏗️</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.whyNow.technical.title">
                Technical Architecture
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <ul className={styles.bulletList}>
                <li>
                  <Translate id="ai101.whyNow.technical.item1">
                    Transformer revolutionary breakthrough
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.whyNow.technical.item2">
                    Attention mechanism innovation
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.whyNow.technical.item3">
                    End-to-end learning paradigm
                  </Translate>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

// Concept Clarification slide
function ConceptClarificationSlide({ ref }) {
  return (
    <section className={styles.slide} id="slide-3" ref={ref}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.slide3.title">
            Concept Clarification
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.slide3.subtitle">
            Relationship between LLM, GenAI, and AGI
          </Translate>
        </p>

        <div className={styles.contentSection}>
          <div className={styles.codeBlock}>
            <Translate id="ai101.concept.hierarchy">
              {`AGI (Artificial General Intelligence)
    ↑ Target direction
GenAI (Generative AI)
    ↑ Current stage
LLM (Large Language Model)
    ↑ Core technology`}
            </Translate>
          </div>

          <div className={styles.contentGrid}>
            <div className={styles.contentCard}>
              <h3 className={styles.cardTitle}>
                <Translate id="ai101.concept.llm.title">
                  LLM
                </Translate>
              </h3>
              <div className={styles.cardContent}>
                <Translate id="ai101.concept.llm.content">
                  Large-scale language models based on Transformer architecture
                </Translate>
              </div>
            </div>

            <div className={styles.contentCard}>
              <h3 className={styles.cardTitle}>
                <Translate id="ai101.concept.genai.title">
                  GenAI
                </Translate>
              </h3>
              <div className={styles.cardContent}>
                <Translate id="ai101.concept.genai.content">
                  AI systems capable of generating text, images, audio and other content
                </Translate>
              </div>
            </div>

            <div className={styles.contentCard}>
              <h3 className={styles.cardTitle}>
                <Translate id="ai101.concept.agi.title">
                  AGI
                </Translate>
              </h3>
              <div className={styles.cardContent}>
                <Translate id="ai101.concept.agi.content">
                  General intelligence that matches or exceeds humans in all cognitive tasks
                </Translate>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

// Generative AI Characteristics slide
function GenerativeAISlide({ ref }) {
  return (
    <section className={styles.slide} id="slide-4" ref={ref}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.slide4.title">
            Characteristics of Generative AI
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.slide4.subtitle">
            From Recognition to Creation
          </Translate>
        </p>

        <div className={styles.contentSection}>
          <div className={styles.cardContent}>
            <h3 className={styles.cardTitle}>🆚 Differences from Traditional AI</h3>
            <div className={styles.comparisonTable}>
              <table>
                <thead>
                  <tr>
                    <th>
                      <Translate id="ai101.genai.differences.traditional">
                        Traditional AI
                      </Translate>
                    </th>
                    <th>
                      <Translate id="ai101.genai.differences.generative">
                        Generative AI
                      </Translate>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>
                      <Translate id="ai101.genai.differences.traditional.item1">
                        Recognition & Classification
                      </Translate>
                    </td>
                    <td>
                      <Translate id="ai101.genai.differences.generative.item1">
                        Content Creation
                      </Translate>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <Translate id="ai101.genai.differences.traditional.item2">
                        Rule-driven
                      </Translate>
                    </td>
                    <td>
                      <Translate id="ai101.genai.differences.generative.item2">
                        Data-driven
                      </Translate>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <Translate id="ai101.genai.differences.traditional.item3">
                        Specialized Systems
                      </Translate>
                    </td>
                    <td>
                      <Translate id="ai101.genai.differences.generative.item3">
                        General Capabilities
                      </Translate>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <Translate id="ai101.genai.differences.traditional.item4">
                        Deterministic Output
                      </Translate>
                    </td>
                    <td>
                      <Translate id="ai101.genai.differences.generative.item4">
                        Probabilistic Generation
                      </Translate>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>


          <div>

            <h3 className={styles.cardTitle}>🔗 <Translate id="ai101.genai.coreRelatedTech.title">Core Related Technologies</Translate></h3>
            <ul className={styles.bulletList}>
              <li><Translate id="ai101.genai.coreRelatedTech.item1">Deep Neural Networks</Translate></li>
              <li><Translate id="ai101.genai.coreRelatedTech.item2">Attention Mechanisms</Translate></li>
              <li><Translate id="ai101.genai.coreRelatedTech.item3">Pre-training & Fine-tuning Paradigm</Translate></li>
              <li><Translate id="ai101.genai.coreRelatedTech.item4">Reinforcement Learning Alignment</Translate></li>
            </ul>
          </div>

        </div>
      </div>
    </section>
  );
}

// Core Learning Technologies slide
function CoreLearningSlide({ ref }) {
  return (
    <section className={styles.slide} id="slide-5" ref={ref}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.slide5.title">
            Core Learning Technologies
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.slide5.subtitle">
            Understanding How AI 'Learns'
          </Translate>
        </p>

        <div className={styles.contentGrid}>
          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🎯</span>
            <h3 className={styles.cardTitle}><Translate id="ai101.coreLearning.supervisedLearning.title">Supervised Learning</Translate></h3>
            <div className={styles.cardContent}>
              <p><Translate id="ai101.coreLearning.supervisedLearning.content">Learning input-output mappings from labeled data</Translate></p>
              <p><em><Translate id="ai101.coreLearning.supervisedLearning.examples">Examples: Image classification, sentiment analysis</Translate></em></p>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🎮</span>
            <h3 className={styles.cardTitle}><Translate id="ai101.coreLearning.reinforcementLearning.title">Reinforcement Learning</Translate></h3>
            <div className={styles.cardContent}>
              <p><Translate id="ai101.coreLearning.reinforcementLearning.content">Optimizing strategies through trial and error with reward signals</Translate></p>
              <p><em><Translate id="ai101.coreLearning.reinforcementLearning.examples">Examples: Game AI, robot control</Translate></em></p>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🔄</span>
            <h3 className={styles.cardTitle}><Translate id="ai101.coreLearning.deepLearning.title">Deep Learning</Translate></h3>
            <div className={styles.cardContent}>
              <p><Translate id="ai101.coreLearning.deepLearning.content">Multi-layer neural networks automatically extracting features</Translate></p>
              <p><em><Translate id="ai101.coreLearning.deepLearning.examples">End-to-end learning of complex patterns</Translate></em></p>
            </div>
          </div>
        </div>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>🏆 <Translate id="ai101.coreLearning.powerCombining.title">The Power of Combining All Three</Translate></h3>
          <div className={styles.codeBlock}>
            <Translate id="ai101.coreLearning.powerCombining.content">Supervised Learning lays foundation → Deep Learning extracts features → Reinforcement Learning optimizes behavior</Translate>
          </div>
        </div>
      </div>
    </section>
  );
}

// AI Training Stages slide
function AITrainingSlide({ ref }) {
  return (
    <section className={styles.slide} id="slide-6" ref={ref}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.slide6.title">
            Three Key Stages of AI Training
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.slide6.subtitle">
            From Raw to Intelligent Transformation
          </Translate>
        </p>

        <div className={styles.contentGrid}>
          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>📚</span>
            <h3 className={styles.cardTitle}><Translate id="ai101.aiTraining.stage1.title">Stage 1: Pre-training</Translate></h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.content.aiTraining.pretraining">
                Pre-training: Learning language patterns from massive text data
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🎯</span>
            <h3 className={styles.cardTitle}><Translate id="ai101.aiTraining.stage2.title">Stage 2: Supervised Fine-tuning</Translate></h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.content.aiTraining.supervised">
                Supervised Fine-tuning: Learning to follow instructions
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🏆</span>
            <h3 className={styles.cardTitle}><Translate id="ai101.aiTraining.stage3.title">Stage 3: Reinforcement Learning</Translate></h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.content.aiTraining.reinforcement">
                Reinforcement Learning: Aligning with human preferences
              </Translate>
            </div>
          </div>
        </div>

        <div className={styles.contentSection}>
          <div className={styles.codeBlock}>
            <Translate id="ai101.aiTraining.process">Raw Text → Language Model → Instruction Follower → Human-Aligned AI</Translate>
          </div>
        </div>
      </div>
    </section>
  );
}

// Neural Networks slide
function NeuralNetworkSlide({ ref }) {
  return (
    <section className={styles.slide} id="slide-7" ref={ref}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.slide7.title">
            Neural Networks: Mimicking Brain Intelligence
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.slide7.subtitle">
            From Biological Inspiration to Artificial Implementation
          </Translate>
        </p>

        <div className={styles.contentGrid}>
          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🧠</span>
            <h3 className={styles.cardTitle}><Translate id="ai101.neuralNetwork.biologicalNeurons.title">Biological Neurons</Translate></h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.content.neuralNetwork.biological">
                Biological neurons transmit electrical signals
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🤖</span>
            <h3 className={styles.cardTitle}><Translate id="ai101.neuralNetwork.artificialNeurons.title">Artificial Neurons</Translate></h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.content.neuralNetwork.artificial">
                Artificial neurons process numerical values
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🏗️</span>
            <h3 className={styles.cardTitle}><Translate id="ai101.neuralNetwork.deepNetworks.title">Deep Networks</Translate></h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.content.neuralNetwork.layers">
                Multiple layers enable complex pattern recognition
              </Translate>
            </div>
          </div>
        </div>


        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>🔗 <Translate id="ai101.neuralNetwork.architecture.title">Network Architecture</Translate></h3>
          <div className={styles.neuralNetworkContainer}>
            <div className={styles.neuralNetworkImage}>
              <img
                className={styles.featureImage}
                alt={"Neural Networks Architecture"}
                src={"/img/portfolio/fullsize/ai101_neural_networks.png"}
              />
            </div>
            <div className={styles.neuralNetworkContent}>
              <div className={styles.codeBlock}>
                <Translate id="ai101.neuralNetwork.architecture.content">{`Input Layer → Hidden Layers → Output Layer
    ↓               ↓                 ↓
Raw Data → Feature Extraction → Predictions`}</Translate>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

// AI Math Foundations slide
function AIMathSlide({ ref }) {
  return (
    <section className={styles.slide} id="slide-8" ref={ref}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.slide8.title">
            Mathematical Foundations of AI
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.slide8.subtitle">
            Emergence of Intelligence in a Probabilistic World
          </Translate>
        </p>

        <div className={styles.contentGrid}>
          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🎲</span>
            <h3 className={styles.cardTitle}><Translate id="ai101.math.probabilityTheory.title">Probability Theory</Translate></h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.content.math.probability">
                Everything is probability - no absolute certainty
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>📊</span>
            <h3 className={styles.cardTitle}><Translate id="ai101.math.statistics.title">Statistics</Translate></h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.content.math.statistics">
                Learning patterns from data through statistical methods
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>⚡</span>
            <h3 className={styles.cardTitle}><Translate id="ai101.math.optimization.title">Optimization</Translate></h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.content.math.optimization">
                Continuously optimizing to find the best solutions
              </Translate>
            </div>
          </div>
        </div>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>🧮 <Translate id="ai101.math.coreConcepts.title">Core Mathematical Concepts</Translate></h3>
          <ul className={styles.bulletList}>
            <li><Translate id="ai101.math.coreConcepts.item1">Linear Algebra: Vector spaces and transformations</Translate></li>
            <li><Translate id="ai101.math.coreConcepts.item2">Calculus: Gradient descent and backpropagation</Translate></li>
            <li><Translate id="ai101.math.coreConcepts.item3">Information Theory: Entropy and compression</Translate></li>
            <li><Translate id="ai101.math.coreConcepts.item4">Graph Theory: Network structures and relationships</Translate></li>
          </ul>
        </div>
      </div>
    </section>
  );
}

// Key Concepts slide
function KeyConceptsSlide({ ref }) {
  return (
    <section className={styles.slide} id="slide-9" ref={ref}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.slide9.title">
            Key Concept Analysis
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.slide9.subtitle">
            Understanding AI's Basic Elements
          </Translate>
        </p>

        <div className={styles.contentGrid}>
          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>⚙️</span>
            <h3 className={styles.cardTitle}><Translate id="ai101.keyConcepts.parameters.title">Parameters</Translate></h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.content.concepts.parameters">
                Parameters: The 'knowledge' stored in neural networks
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🔤</span>
            <h3 className={styles.cardTitle}><Translate id="ai101.keyConcepts.tokens.title">Tokens</Translate></h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.content.concepts.tokens">
                Tokens: Basic units of text processing
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>👁️</span>
            <h3 className={styles.cardTitle}><Translate id="ai101.keyConcepts.attention.title">Attention</Translate></h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.content.concepts.attention">
                Attention: Mechanism for focusing on relevant information
              </Translate>
            </div>
          </div>
        </div>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>📈 <Translate id="ai101.keyConcepts.scaleComparison.title">Scale Comparison</Translate></h3>
          <div className={styles.comparisonTable}>
            <table>
              <thead>
                <tr>
                  <th><Translate id="ai101.keyConcepts.table.model">Model</Translate></th>
                  <th><Translate id="ai101.keyConcepts.table.parameters">Parameters</Translate></th>
                  <th><Translate id="ai101.keyConcepts.table.trainingData">Training Data</Translate></th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>GPT-3</td>
                  <td>175B</td>
                  <td>300B tokens</td>
                </tr>
                <tr>
                  <td>GPT-4</td>
                  <td>~1.7T</td>
                  <td>~13T tokens</td>
                </tr>
                <tr>
                  <td><Translate id="ai101.keyConcepts.table.humanBrain">Human Brain</Translate></td>
                  <td>~86B neurons</td>
                  <td><Translate id="ai101.keyConcepts.table.lifetimeExperience">Lifetime experience</Translate></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </section>
  );
}

// Intelligence Nature slide
function IntelligenceNatureSlide({ ref }) {
  return (
    <section className={styles.slide} id="slide-10" ref={ref}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.slide10.title">
            The Nature of Intelligence: Information Compression?
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.slide10.subtitle">
            Extracting Patterns from Data
          </Translate>
        </p>

        <div className={styles.contentGrid}>
          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🗜️</span>
            <h3 className={styles.cardTitle}><Translate id="ai101.intelligence.informationCompression.title">Information Compression</Translate></h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.content.intelligence.compression">
                Intelligence may be the ability to compress information efficiently
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🔍</span>
            <h3 className={styles.cardTitle}><Translate id="ai101.intelligence.patternRecognition.title">Pattern Recognition</Translate></h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.content.intelligence.patterns">
                Finding the simplest rules that explain complex phenomena
              </Translate>
            </div>
          </div>
        </div>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>🧠 <Translate id="ai101.intelligence.asCompression.title">Intelligence as Compression</Translate></h3>
          <div className={styles.codeBlock}>
            <Translate id="ai101.intelligence.asCompression.content">{`Raw Data (Terabytes) → Compressed Knowledge (Gigabytes) → Predictions

Example: Learning language from billions of words
→ Compressed into grammar rules and patterns
→ Generate coherent new sentences`}</Translate>
          </div>
        </div>
      </div>
    </section>
  );
}

// Scaling Law slide
function ScalingLawSlide({ ref }) {
  return (
    <section className={styles.slide} id="slide-11" ref={ref}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.slide11.title">
            Scaling Law: The Magic of Scale
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.slide11.subtitle">
            Bigger is Better?
          </Translate>
        </p>

        <div className={styles.contentGrid}>
          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>📈</span>
            <h3 className={styles.cardTitle}><Translate id="ai101.scaling.moreParameters.title">More Parameters</Translate></h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.content.scaling.bigger">
                More parameters → Better performance
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>💾</span>
            <h3 className={styles.cardTitle}><Translate id="ai101.scaling.moreData.title">More Data</Translate></h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.content.scaling.data">
                More data → More knowledge
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>⚡</span>
            <h3 className={styles.cardTitle}><Translate id="ai101.scaling.moreCompute.title">More Compute</Translate></h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.content.scaling.compute">
                More computation → Better training
              </Translate>
            </div>
          </div>
        </div>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>📊 <Translate id="ai101.scaling.trends.title">Scaling Trends</Translate></h3>
          <ul className={styles.bulletList}>
            <li><Translate id="ai101.scaling.trends.item1">Performance improves predictably with scale</Translate></li>
            <li><Translate id="ai101.scaling.trends.item2">Emergent abilities appear at certain thresholds</Translate></li>
            <li><Translate id="ai101.scaling.trends.item3">But scaling has physical and economic limits</Translate></li>
            <li><Translate id="ai101.scaling.trends.item4">Efficiency improvements become crucial</Translate></li>
          </ul>
        </div>
      </div>
    </section>
  );
}

// AI Understanding slide
function AIUnderstandingSlide({ ref }) {
  return (
    <section className={styles.slide} id="slide-12" ref={ref}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.slide12.title">
            Does AI Really 'Understand'?
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.slide12.subtitle">
            Statistical Patterns vs True Understanding
          </Translate>
        </p>

        <div className={styles.contentGrid}>
          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>📊</span>
            <h3 className={styles.cardTitle}><Translate id="ai101.understanding.statisticalMastery.title">Statistical Mastery</Translate></h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.content.understanding.statistical">
                AI excels at statistical pattern matching
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🤔</span>
            <h3 className={styles.cardTitle}><Translate id="ai101.understanding.semanticUnderstanding.title">Semantic Understanding?</Translate></h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.content.understanding.semantic">
                But does it truly understand meaning?
              </Translate>
            </div>
          </div>
        </div>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>🎭 <Translate id="ai101.understanding.chineseRoom.title">The Chinese Room Argument</Translate></h3>
          <div className={styles.codeBlock}>
            <Translate id="ai101.understanding.chineseRoom.content">{`Person in room follows rules to respond to Chinese characters
↓
Appears to understand Chinese, but actually doesn't
↓
Similarly, AI might simulate understanding without true comprehension`}</Translate>
          </div>

          <h3 className={styles.cardTitle}>🔬 <Translate id="ai101.understanding.currentEvidence.title">Current Evidence</Translate></h3>
          <ul className={styles.bulletList}>
            <li><Translate id="ai101.understanding.currentEvidence.item1">AI shows remarkable language capabilities</Translate></li>
            <li><Translate id="ai101.understanding.currentEvidence.item2">Can reason about abstract concepts</Translate></li>
            <li><Translate id="ai101.understanding.currentEvidence.item3">But lacks grounded experience in the world</Translate></li>
            <li><Translate id="ai101.understanding.currentEvidence.item4">Understanding vs. sophisticated pattern matching remains debated</Translate></li>
          </ul>
        </div>
      </div>
    </section>
  );
}

// AGI Potential slide
function AGIPotentialSlide({ ref }) {
  return (
    <section className={styles.slide} id="slide-13" ref={ref}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.slide13.title">
            Can AI Surpass Humans?
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.slide13.subtitle">
            Journey Towards Artificial General Intelligence
          </Translate>
        </p>

        <div className={styles.contentGrid}>
          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🎯</span>
            <h3 className={styles.cardTitle}><Translate id="ai101.agi.currentState.title">Current State</Translate></h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.content.agi.current">
                Current AI: Narrow, specialized capabilities
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🚀</span>
            <h3 className={styles.cardTitle}><Translate id="ai101.agi.agiVision.title">AGI Vision</Translate></h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.content.agi.future">
                AGI Goal: General intelligence across all domains
              </Translate>
            </div>
          </div>
        </div>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>🏆 <Translate id="ai101.agi.vsHuman.title">AI vs Human Capabilities</Translate></h3>
          <div className={styles.comparisonTable}>
            <table>
              <thead>
                <tr>
                  <th><Translate id="ai101.agi.table.domain">Domain</Translate></th>
                  <th><Translate id="ai101.agi.table.aiStatus">AI Status</Translate></th>
                  <th><Translate id="ai101.agi.table.humanLevel">Human Level</Translate></th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td><Translate id="ai101.agi.table.chessGo">Chess/Go</Translate></td>
                  <td><Translate id="ai101.agi.table.superhuman">✅ Superhuman</Translate></td>
                  <td><Translate id="ai101.agi.table.surpassed">Surpassed</Translate></td>
                </tr>
                <tr>
                  <td><Translate id="ai101.agi.table.imageRecognition">Image Recognition</Translate></td>
                  <td><Translate id="ai101.agi.table.humanLevelStatus">✅ Human-level</Translate></td>
                  <td><Translate id="ai101.agi.table.matched">Matched</Translate></td>
                </tr>
                <tr>
                  <td><Translate id="ai101.agi.table.languageTasks">Language Tasks</Translate></td>
                  <td><Translate id="ai101.agi.table.approaching">🔄 Approaching</Translate></td>
                  <td><Translate id="ai101.agi.table.nearHuman">Near human</Translate></td>
                </tr>
                <tr>
                  <td><Translate id="ai101.agi.table.generalReasoning">General Reasoning</Translate></td>
                  <td><Translate id="ai101.agi.table.uncertain">❓ Uncertain</Translate></td>
                  <td><Translate id="ai101.agi.table.belowHuman">Below human</Translate></td>
                </tr>
                <tr>
                  <td><Translate id="ai101.agi.table.creativity">Creativity</Translate></td>
                  <td><Translate id="ai101.agi.table.emerging">🎨 Emerging</Translate></td>
                  <td><Translate id="ai101.agi.table.debated">Debated</Translate></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </section>
  );
}

// AI Threats slide
function AIThreatsSlide({ ref }) {
  return (
    <section className={styles.slide} id="slide-14" ref={ref}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.slide14.title">
            AI Threats: Worry or Embrace?
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.slide14.subtitle">
            Rational View of AI Risks
          </Translate>
        </p>

        <div className={styles.contentGrid}>
          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>💼</span>
            <h3 className={styles.cardTitle}><Translate id="ai101.threats.jobDisplacement.title">Job Displacement</Translate></h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.content.threats.job">
                Job displacement in certain sectors
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>📰</span>
            <h3 className={styles.cardTitle}><Translate id="ai101.threats.misinformation.title">Misinformation</Translate></h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.content.threats.misinformation">
                Potential for generating misinformation
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>⚖️</span>
            <h3 className={styles.cardTitle}><Translate id="ai101.threats.biasAmplification.title">Bias Amplification</Translate></h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.content.threats.bias">
                Amplification of existing biases
              </Translate>
            </div>
          </div>
        </div>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>🛡️ <Translate id="ai101.threats.mitigation.title">Mitigation Strategies</Translate></h3>
          <ul className={styles.bulletList}>
            <li><Translate id="ai101.threats.mitigation.item1">Develop AI governance and regulation frameworks</Translate></li>
            <li><Translate id="ai101.threats.mitigation.item2">Invest in education and reskilling programs</Translate></li>
            <li><Translate id="ai101.threats.mitigation.item3">Promote responsible AI development practices</Translate></li>
            <li><Translate id="ai101.threats.mitigation.item4">Foster human-AI collaboration rather than replacement</Translate></li>
          </ul>
        </div>
      </div>
    </section>
  );
}

// AI Survival slide
function AISurvivalSlide({ ref }) {
  return (
    <section className={styles.slide} id="slide-15" ref={ref}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.slide15.title">
            Survival in the AI Era
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.slide15.subtitle">
            Adapt to Change, Embrace the Future
          </Translate>
        </p>

        <div className={styles.contentGrid}>
          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🔄</span>
            <h3 className={styles.cardTitle}><Translate id="ai101.survival.continuousLearning.title">Continuous Learning</Translate></h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.content.survival.adapt">
                Embrace change and continuous learning
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🤝</span>
            <h3 className={styles.cardTitle}><Translate id="ai101.survival.aiCollaboration.title">AI Collaboration</Translate></h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.content.survival.collaborate">
                Learn to work with AI as a partner
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>💡</span>
            <h3 className={styles.cardTitle}><Translate id="ai101.survival.humanUniqueness.title">Human Uniqueness</Translate></h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.content.survival.human">
                Focus on uniquely human capabilities
              </Translate>
            </div>
          </div>
        </div>

        <div className={styles.contentSection}>
          <h3 className={styles.cardTitle}>🎯 <Translate id="ai101.survival.strategicApproach.title">Strategic Approach</Translate></h3>
          <div className={styles.codeBlock}>
            <Translate id="ai101.survival.strategicApproach.content">{`Short-term: Learn AI tools and workflows
Medium-term: Develop AI-human collaboration skills
Long-term: Focus on creativity, empathy, and complex reasoning`}</Translate>
          </div>
        </div>
      </div>
    </section>
  );
}

// Essential Skills slide
function EssentialSkillsSlide({ ref }) {
  return (
    <section className={styles.slide} id="slide-16" ref={ref}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.slide16.title">
            Essential Skills for the AI Era
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.slide16.subtitle">
            Core Competencies for the Future
          </Translate>
        </p>

        <div className={styles.contentGrid}>
          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>💡</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide16.criticalThinking.title">
                Critical Thinking
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide16.criticalThinking.content">
                Ability to analyze, evaluate, and synthesize information
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🎨</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide16.creativity.title">
                Creativity
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide16.creativity.content">
                Generate novel ideas and innovative solutions
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>❤️</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide16.emotionalIntelligence.title">
                Emotional Intelligence
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide16.emotionalIntelligence.content">
                Understanding and managing emotions in human interactions
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🤖</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide16.aiLiteracy.title">
                AI Literacy
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide16.aiLiteracy.content">
                Understanding AI capabilities, limitations, and ethical implications
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🤝</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide16.systemsThinking.title">
                Systems Thinking
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide16.systemsThinking.content">
                Understanding complex interconnections and relationships
              </Translate>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🔄</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.slide16.adaptability.title">
                Leadership
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <Translate id="ai101.slide16.adaptability.content">
                Leadership of human-AI hybrid teams, with judgment and decision-making capabilities
              </Translate>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

// GPT Overview slide
function GPTOverviewSlide({ ref }) {
  return (
    <section className={styles.slide} id="slide-gpt-overview" ref={ref}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.gpt.title">
            GPT (Generative Pre-trained Transformer)
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.gpt.subtitle">
            Understanding the Core Technology of Modern AI
          </Translate>
        </p>

        <div className={styles.contentGrid}>
          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>💡</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.gpt.definition.title">
                Core Definition
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <ul className={styles.bulletList}>
                <li>
                  <Translate id="ai101.gpt.definition.item1">
                    Generative Pre-trained Transformer: Large language model based on Transformer architecture
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.gpt.definition.item2">
                    Autoregressive language model: Generates text by predicting the next word
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.gpt.definition.item3">
                    Unsupervised pre-training + Supervised fine-tuning: Typical representative of two-stage training paradigm
                  </Translate>
                </li>
              </ul>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>⚡</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.gpt.features.title">
                Key Features
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <ul className={styles.bulletList}>
                <li>
                  <Translate id="ai101.gpt.features.item1">
                    Unidirectional attention mechanism: Can only see previous text, suitable for text generation tasks
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.gpt.features.item2">
                    Large-scale parameters: From GPT-1's 117 million to GPT-4's hundreds of billions of parameters
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.gpt.features.item3">
                    Powerful zero-shot and few-shot learning capabilities
                  </Translate>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

// GPT Architecture slide
function GPTArchitectureSlide({ ref }) {
  return (
    <section className={styles.slide} id="slide-gpt-architecture" ref={ref}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.gptArchitecture.title">
            GPT Core Technical Architecture
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.gptArchitecture.subtitle">
            Deep Understanding of GPT's Technical Foundation
          </Translate>
        </p>

        <div className={styles.contentGrid}>
          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🔧</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.gptArchitecture.transformer.title">
                Transformer Decoder
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <ul className={styles.bulletList}>
                <li>
                  <Translate id="ai101.gptArchitecture.transformer.item1">
                    Multi-head self-attention: Captures long-range dependencies
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.gptArchitecture.transformer.item2">
                    Positional encoding: Understands positional information in text sequences
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.gptArchitecture.transformer.item3">
                    Residual connections + Layer normalization: Stabilizes training process
                  </Translate>
                </li>
              </ul>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>📚</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.gptArchitecture.pretraining.title">
                Pre-training Strategy
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <ul className={styles.bulletList}>
                <li>
                  <Translate id="ai101.gptArchitecture.pretraining.item1">
                    Next word prediction: Learning language patterns from large-scale text corpora
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.gptArchitecture.pretraining.item2">
                    Causal masking: Ensures access only to previous words
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.gptArchitecture.pretraining.item3">
                    Large-scale data: Diverse data sources including internet text, books, news
                  </Translate>
                </li>
              </ul>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🎯</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.gptArchitecture.alignment.title">
                Fine-tuning and Alignment
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <ul className={styles.bulletList}>
                <li>
                  <Translate id="ai101.gptArchitecture.alignment.item1">
                    Instruction fine-tuning: Improves model's ability to follow instructions
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.gptArchitecture.alignment.item2">
                    Reinforcement Learning from Human Feedback (RLHF): Aligns model outputs with human preferences
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.gptArchitecture.alignment.item3">
                    Safety filtering: Reduces generation of harmful content
                  </Translate>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

// GPT Capabilities slide
function GPTCapabilitiesSlide({ ref }) {
  return (
    <section className={styles.slide} id="slide-gpt-capabilities" ref={ref}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.gptCapabilities.title">
            GPT Core Capabilities and Applications
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.gptCapabilities.subtitle">
            From Text Generation to Intelligent Reasoning
          </Translate>
        </p>

        <div className={styles.contentGrid}>
          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>✍️</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.gptCapabilities.textGeneration.title">
                Text Generation Capabilities
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <ul className={styles.bulletList}>
                <li>
                  <Translate id="ai101.gptCapabilities.textGeneration.item1">
                    Creative writing: Stories, poetry, script creation
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.gptCapabilities.textGeneration.item2">
                    Technical documentation: API docs, user manuals, technical reports
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.gptCapabilities.textGeneration.item3">
                    Marketing content: Ad copy, product descriptions, social media content
                  </Translate>
                </li>
              </ul>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🤔</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.gptCapabilities.understanding.title">
                Understanding and Reasoning
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <ul className={styles.bulletList}>
                <li>
                  <Translate id="ai101.gptCapabilities.understanding.item1">
                    Reading comprehension: Answering complex text-based questions
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.gptCapabilities.understanding.item2">
                    Logical reasoning: Solving mathematical problems and logic puzzles
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.gptCapabilities.understanding.item3">
                    Knowledge Q&A: Cross-domain encyclopedic knowledge queries
                  </Translate>
                </li>
              </ul>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>💻</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.gptCapabilities.coding.title">
                Code Generation
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <ul className={styles.bulletList}>
                <li>
                  <Translate id="ai101.gptCapabilities.coding.item1">
                    Program writing: Generating code based on requirements
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.gptCapabilities.coding.item2">
                    Code explanation: Understanding and commenting existing code
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.gptCapabilities.coding.item3">
                    Debugging assistance: Finding and fixing code errors
                  </Translate>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

// GPT Limitations slide
function GPTLimitationsSlide({ ref }) {
  return (
    <section className={styles.slide} id="slide-gpt-limitations" ref={ref}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.gptLimitations.title">
            GPT Technical Advantages and Limitations
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.gptLimitations.subtitle">
            Rational Understanding of GPT's Capability Boundaries
          </Translate>
        </p>

        <div className={styles.contentGrid}>
          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>✅</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.gptLimitations.advantages.title">
                Main Advantages
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <ul className={styles.bulletList}>
                <li>
                  <Translate id="ai101.gptLimitations.advantages.item1">
                    Strong generalization: One model handles multiple tasks
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.gptLimitations.advantages.item2">
                    In-context learning: Quickly adapts to new tasks through examples
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.gptLimitations.advantages.item3">
                    Creative output: Generates novel and useful content
                  </Translate>
                </li>
              </ul>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>⚠️</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.gptLimitations.limitations.title">
                Current Limitations
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <ul className={styles.bulletList}>
                <li>
                  <Translate id="ai101.gptLimitations.limitations.item1">
                    Hallucination issues: May generate seemingly reasonable but actually incorrect information
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.gptLimitations.limitations.item2">
                    Knowledge cutoff: Training data has time limitations
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.gptLimitations.limitations.item3">
                    High computational cost: Inference requires significant computational resources
                  </Translate>
                </li>
                <li>
                  <Translate id="ai101.gptLimitations.limitations.item4">
                    Poor interpretability: Difficult to understand model's decision-making process
                  </Translate>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

// Concepts Explained slide
function ConceptsExplainedSlide({ ref }) {
  return (
    <section className={styles.slide} id="slide-concepts-explained" ref={ref}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.conceptsExplained.title">
            Parameters, Dimensions, and Tokens Explained
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.conceptsExplained.subtitle">
            Understanding Core Concepts of Large Language Models
          </Translate>
        </p>

        <div className={styles.contentGrid}>
          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🧩</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.conceptsExplained.token.title">
                Token
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <p>
                <Translate id="ai101.conceptsExplained.token.definition">
                  Definition: Token is the smallest unit for model text processing, can be a single character, word, or even part of a word. Created by a tokenizer that splits raw text.
                </Translate>
              </p>
              <div className={styles.codeBlock}>
                <Translate id="ai101.conceptsExplained.token.examples">
                  Examples:
                  English: "ChatGPT is great" might be split into: ["Chat", "G", "PT", " is", " great"]
                  Chinese: "大模型很好用" might be split into: ["大", "模型", "很", "好", "用"]
                </Translate>
              </div>
              <p className={styles.analogy}>
                ⌨️ <Translate id="ai101.conceptsExplained.token.analogy">
                  Analogy: If you think of a sentence as a wall built with blocks, tokens are each individual block.
                </Translate>
              </p>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>📦</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.conceptsExplained.dimensions.title">
                Dimensions
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <p>
                <Translate id="ai101.conceptsExplained.dimensions.definition">
                  Definition: Dimension is the position or "length" of each data point in a vector or matrix. Commonly used to describe the vector space size in hidden layers.
                </Translate>
              </p>
              <div className={styles.codeBlock}>
                <Translate id="ai101.conceptsExplained.dimensions.usage">
                  Common usage:
                  Word vector dimensions (embedding size): e.g., a word mapped to a 768-dimensional vector.
                  Hidden layer dimensions (hidden size): represents the vector length of each neuron's output in each layer.
                </Translate>
              </div>
              <p className={styles.analogy}>
                📦 <Translate id="ai101.conceptsExplained.dimensions.analogy">
                  Analogy: If a token is a product, dimensions are the number of "feature labels" it has, like color, size, purpose, etc.
                </Translate>
              </p>
            </div>
          </div>

          <div className={styles.contentCard}>
            <span className={styles.cardIcon}>🧠</span>
            <h3 className={styles.cardTitle}>
              <Translate id="ai101.conceptsExplained.parameters.title">
                Parameters
              </Translate>
            </h3>
            <div className={styles.cardContent}>
              <p>
                <Translate id="ai101.conceptsExplained.parameters.definition">
                  Definition: Parameters are the "knowledge" learned by the model during training. They include weights and biases in neural networks.
                </Translate>
              </p>
              <p>
                <Translate id="ai101.conceptsExplained.parameters.scale">
                  Scale: GPT-3 has 175 billion parameters, GPT-4 is estimated to have even more.
                </Translate>
              </p>
              <p className={styles.analogy}>
                🧠 <Translate id="ai101.conceptsExplained.parameters.analogy">
                  Analogy: Think of the model as a brain, parameters are the "memory connections" or "experiences" formed in that brain.
                </Translate>
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

// AI Collaboration slide
function AICollaborationSlide({ ref }) {
  return (
    <section className={styles.slide} id="slide-17" ref={ref}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          <Translate id="ai101.slide17.title">
            The Art of AI Collaboration
          </Translate>
        </Heading>
        <p className={styles.slideSubtitle}>
          <Translate id="ai101.slide17.subtitle">
            Short, Medium, and Long-term Strategic Planning
          </Translate>
        </p>

        <div className={styles.contentSection}>
          <div className={styles.timeline}>
            <div className={styles.timelineItem}>
              <div className={styles.timelineYear}>
                <Translate id="ai101.slide17.shortTerm.title">
                  Short-term (1-2 years)
                </Translate>
              </div>
              <div className={styles.timelineEvent}>
                <ul className={styles.bulletList}>
                  <li>
                    <Translate id="ai101.slide17.shortTerm.item1">
                      Learn to use AI tools effectively
                    </Translate>
                  </li>
                  <li>
                    <Translate id="ai101.slide17.shortTerm.item2">
                      Understand AI capabilities and limitations
                    </Translate>
                  </li>
                  <li>
                    <Translate id="ai101.slide17.shortTerm.item3">
                      Develop prompt engineering skills
                    </Translate>
                  </li>
                </ul>
              </div>
            </div>

            <div className={styles.timelineItem}>
              <div className={styles.timelineYear}>
                <Translate id="ai101.slide17.mediumTerm.title">
                  Medium-term (3-5 years)
                </Translate>
              </div>
              <div className={styles.timelineEvent}>
                <ul className={styles.bulletList}>
                  <li>
                    <Translate id="ai101.slide17.mediumTerm.item1">
                      Master human-AI workflow integration
                    </Translate>
                  </li>
                  <li>
                    <Translate id="ai101.slide17.mediumTerm.item2">
                      Develop AI-assisted teaching methodologies
                    </Translate>
                  </li>
                  <li>
                    <Translate id="ai101.slide17.mediumTerm.item3">
                      Build AI literacy curriculum
                    </Translate>
                  </li>
                </ul>
              </div>
            </div>

            <div className={styles.timelineItem}>
              <div className={styles.timelineYear}>
                <Translate id="ai101.slide17.longTerm.title">
                  Long-term (5+ years)
                </Translate>
              </div>
              <div className={styles.timelineEvent}>
                <ul className={styles.bulletList}>
                  <li>
                    <Translate id="ai101.slide17.longTerm.item1">
                      Lead AI-enhanced educational transformation
                    </Translate>
                  </li>
                  <li>
                    <Translate id="ai101.slide17.longTerm.item2">
                      Focus on uniquely human educational values
                    </Translate>
                  </li>
                  <li>
                    <Translate id="ai101.slide17.longTerm.item3">
                      Shape the future of human-AI collaboration
                    </Translate>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

// Navigation component
function Navigation({ currentSlide, totalSlides, onSlideChange }) {
  return (
    <div className={styles.navigation}>
      <button
        className={styles.navButton}
        onClick={() => onSlideChange(Math.max(0, currentSlide - 1))}
        disabled={currentSlide === 0}
      >
        ←
      </button>
      <span style={{ margin: '0 10px', fontSize: '12px' }}>
        {currentSlide + 1} / {totalSlides}
      </span>
      <button
        className={styles.navButton}
        onClick={() => onSlideChange(Math.min(totalSlides - 1, currentSlide + 1))}
        disabled={currentSlide === totalSlides - 1}
      >
        →
      </button>
    </div>
  );
}

export default function AI101() {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const slideRefs = useRef([]);
  const totalSlides = useRef(0);
  const [visibleSlide, setVisibleSlide] = useState(0);

  // Function to scroll to a specific slide
  const scrollToSlide = useCallback((index) => {
    if (slideRefs.current[index]) {
      slideRefs.current[index].scrollIntoView({ behavior: 'smooth' });
    }
  }, []);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyPress = (event) => {
      if (event.key === 'ArrowRight' || event.key === ' ') {
        setCurrentSlide(prev => {
          const nextSlide = Math.min(totalSlides.current - 1, visibleSlide + 1);
          scrollToSlide(nextSlide);
          return nextSlide;
        });
      } else if (event.key === 'ArrowLeft') {
        setCurrentSlide(prev => {
          const prevSlide = Math.max(0, visibleSlide - 1);
          scrollToSlide(prevSlide);
          return prevSlide;
        });
      } else if (event.key === 'p' || event.key === 'P') {
        toggleFullscreen();
      } else if (event.key === 'Escape' && isFullscreen) {
        exitFullscreen();
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [isFullscreen, visibleSlide, scrollToSlide]);

  // Scroll to current slide when it changes
  useEffect(() => {
    scrollToSlide(currentSlide);
  }, [currentSlide, scrollToSlide]);

  // Function to add a slide ref
  const addSlideRef = useCallback((ref) => {
    if (ref && !slideRefs.current.includes(ref)) {
      slideRefs.current.push(ref);
      totalSlides.current = slideRefs.current.length;
    }
  }, []);

  // Handle fullscreen change
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(document.fullscreenElement !== null);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  // Toggle fullscreen
  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen().catch(err => {
        console.error(`Error attempting to enable fullscreen: ${err.message}`);
      });
    } else {
      exitFullscreen();
    }

    const nav = document.querySelector('nav');
    if (nav) {
      if (isFullscreen) {
        nav.style.display = 'block';
      } else {
        nav.style.display = 'none';
      }
    }
  };

  // Exit fullscreen
  const exitFullscreen = () => {
    if (document.fullscreenElement) {
      document.exitFullscreen().catch(err => {
        console.error(`Error attempting to exit fullscreen: ${err.message}`);
      });
    }
  };

  // 新增：使用 Intersection Observer 跟踪可见幻灯片
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const index = slideRefs.current.indexOf(entry.target);
            if (index !== -1) {
              setVisibleSlide(index);
            }
          }
        });
      },
      {
        threshold: 0.5, // 当幻灯片有50%可见时触发
      }
    );

    // 观察所有幻灯片
    slideRefs.current.forEach((slide) => {
      if (slide) {
        observer.observe(slide);
      }
    });

    return () => {
      // 清理观察者
      slideRefs.current.forEach((slide) => {
        if (slide) {
          observer.unobserve(slide);
        }
      });
    };
  }, []);

  return (
    <Layout
      title={translate({
        id: 'ai101.title',
        message: 'AI and Education: Opportunities and Challenges in the AI Era'
      })}
      description={translate({
        id: 'ai101.subtitle',
        message: 'A Special Lecture for University Teachers'
      })}
    >
      <AI101StructuredData />
      <div className={styles.slidesContainer}>
        <Navigation
          currentSlide={visibleSlide}
          totalSlides={totalSlides.current}
          onSlideChange={(index) => {
            setCurrentSlide(index);
            scrollToSlide(index);
          }}
        />

        <CoverSlide ref={addSlideRef} />
        <AIHistorySlide ref={addSlideRef} />
        <WhyNowSlide ref={addSlideRef} />
        <EndToEndLearningSlide ref={addSlideRef} />
        <ConceptClarificationSlide ref={addSlideRef} />
        <GPTOverviewSlide ref={addSlideRef} />
        <GPTArchitectureSlide ref={addSlideRef} />
        <GPTCapabilitiesSlide ref={addSlideRef} />
        <GPTLimitationsSlide ref={addSlideRef} />
        <GenerativeAISlide ref={addSlideRef} />
        <NeuralNetworkSlide ref={addSlideRef} />
        <ConceptsExplainedSlide ref={addSlideRef} />
        <CoreLearningSlide ref={addSlideRef} />
        <AITrainingSlide ref={addSlideRef} />
        <AIMathSlide ref={addSlideRef} />
        <IntelligenceNatureSlide ref={addSlideRef} />
        <ScalingLawSlide ref={addSlideRef} />
        <AIUnderstandingSlide ref={addSlideRef} />
        <LLMHallucinationSlide ref={addSlideRef} />
        <AGIPotentialSlide ref={addSlideRef} />
        <AIThreatsSlide ref={addSlideRef} />
        <AISurvivalSlide ref={addSlideRef} />
        <EssentialSkillsSlide ref={addSlideRef} />
        <HumanAIQuadrantSlide ref={addSlideRef} />
        <AICommunicationSlide ref={addSlideRef} />
        {/* <GenerativeEraLearningSlide ref={addSlideRef} /> */}
        <AILiteracySlide ref={addSlideRef} />

        <HigherOrderThinkingSlide ref={addSlideRef} />
        <CognitiveOffloadingSlide ref={addSlideRef} />
        <ResponsibleAIFrameworksSlide ref={addSlideRef} />
        <HumanAICollaborationSlide ref={addSlideRef} />
        <EducationalPracticeSlide ref={addSlideRef} />
        <EducationalTransformationSlide ref={addSlideRef} />
        <EmergingEducationalConceptsSlide ref={addSlideRef} />
        <AIEducationalParadigmsSlide ref={addSlideRef} />
        <LifelongLearningSlide ref={addSlideRef} />
        
        <EducationalAISlide ref={addSlideRef} />
        <FunBlocksAISlide ref={addSlideRef} />
        <InnovationWithAISlide ref={addSlideRef} />
        <LinearThinkingSlide ref={addSlideRef} />
        <AIEnhancedThinkingSlide ref={addSlideRef} />
        <SummaryOutlookSlide ref={addSlideRef} />
        <ThankYouSlide ref={addSlideRef} />
      </div>
    </Layout>
  );
}