import { useState, useEffect, useRef, useCallback } from 'react';
import Layout from '@theme/Layout';
import Heading from '@theme/Heading';
import styles from './epistemology-ai.module.css';

// 封面幻灯片
function CoverSlide({ isActive }) {
  return (
    <section className={`${styles.slide} ${isActive ? styles.active : ''}`}>
      <div className={styles.slideContent}>
        <Heading as="h1" className={styles.mainTitle}>
          认识论、知识论与人工智能的交织
        </Heading>
        <p className={styles.subtitle}>
          概念分野、理论挑战与未来图景
        </p>
        <div className={styles.decorativeElements}>
          <div className={styles.circle}></div>
          <div className={styles.triangle}></div>
          <div className={styles.square}></div>
        </div>
      </div>
    </section>
  );
}

// 目录幻灯片
function OutlineSlide({ isActive }) {
  return (
    <section className={`${styles.slide} ${isActive ? styles.active : ''}`}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          演讲大纲
        </Heading>
        <div className={styles.outlineGrid}>
          <div className={styles.outlineSection}>
            <h3>I. 知识的基础</h3>
            <ul>
              <li>认识论与知识论的分野</li>
              <li>古典三重定义(JTB)</li>
              <li>盖梯尔问题的挑战</li>
              <li>理性主义与经验主义</li>
            </ul>
          </div>
          <div className={styles.outlineSection}>
            <h3>II. 人工智能范式</h3>
            <ul>
              <li>AI的定义与目标</li>
              <li>符号主义与连接主义</li>
              <li>知识表示与推理</li>
            </ul>
          </div>
          <div className={styles.outlineSection}>
            <h3>III. AI对认识论的冲击</h3>
            <ul>
              <li>AI能否"拥有知识"？</li>
              <li>大型语言模型的理解</li>
              <li>中文屋论证的意义</li>
            </ul>
          </div>
          <div className={styles.outlineSection}>
            <h3>IV. 认知挑战与未来</h3>
            <ul>
              <li>"黑箱"问题</li>
              <li>算法偏见</li>
              <li>神经符号AI</li>
              <li>人机认知增强</li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
}

// 第一部分：知识的基础
function KnowledgeFoundationSlide({ isActive }) {
  return (
    <section className={`${styles.slide} ${isActive ? styles.active : ''}`}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          I. 知识的基础：认识论框架
        </Heading>
        <div className={styles.contentGrid}>
          <div className={styles.conceptCard}>
            <h3>认识论 vs 知识论</h3>
            <div className={styles.comparison}>
              <div className={styles.comparisonItem}>
                <strong>认识论</strong>
                <p>关注认知主体如何获取客体信息的<em>动态过程</em></p>
                <p>侧重于认知过程的追溯与复演</p>
              </div>
              <div className={styles.comparisonItem}>
                <strong>知识论</strong>
                <p>关注信念自身合法性根据的<em>静态审视</em></p>
                <p>侧重于知识的辩护、真理性和信念关系</p>
              </div>
            </div>
          </div>
          <div className={styles.conceptCard}>
            <h3>核心问题</h3>
            <ul className={styles.bulletList}>
              <li>什么是知识？</li>
              <li>知识如何获得？</li>
              <li>知识的界限在哪里？</li>
              <li>如何区分知识与信念？</li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
}

// JTB定义幻灯片
function JTBSlide({ isActive }) {
  return (
    <section className={`${styles.slide} ${isActive ? styles.active : ''}`}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          古典三重定义：被证成的真实信念 (JTB)
        </Heading>
        <div className={styles.jtbContainer}>
          <div className={styles.jtbDefinition}>
            <p>一个主体S拥有关于命题P的知识，当且仅当：</p>
          </div>
          <div className={styles.jtbComponents}>
            <div className={styles.jtbComponent}>
              <div className={styles.componentIcon}>T</div>
              <h3>真理 (Truth)</h3>
              <p>P是真的</p>
            </div>
            <div className={styles.jtbComponent}>
              <div className={styles.componentIcon}>B</div>
              <h3>信念 (Belief)</h3>
              <p>S相信P</p>
            </div>
            <div className={styles.jtbComponent}>
              <div className={styles.componentIcon}>J</div>
              <h3>证成 (Justification)</h3>
              <p>S对P的相信是有证成的</p>
            </div>
          </div>
          <div className={styles.jtbExample}>
            <h4>例子：</h4>
            <p>仅仅相信一个真实的事情并不足以构成知识。病人相信自己会康复，即使日后确实康复，也不能说他当时"知道"会好，因为缺乏充分的证成。</p>
          </div>
        </div>
      </div>
    </section>
  );
}

// 盖梯尔问题幻灯片
function GettierSlide({ isActive }) {
  return (
    <section className={`${styles.slide} ${isActive ? styles.active : ''}`}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          盖梯尔问题：对JTB的挑战
        </Heading>
        <div className={styles.gettierContainer}>
          <div className={styles.gettierProblem}>
            <h3>核心问题</h3>
            <p>即使满足JTB三个条件，有时仍不构成真正的知识</p>
          </div>
          <div className={styles.gettierExample}>
            <h4>经典案例：福特车例子</h4>
            <div className={styles.exampleSteps}>
              <div className={styles.step}>
                <span className={styles.stepNumber}>1</span>
                <p>看到同事琼斯开福特车，有证成地相信"琼斯拥有福特车"</p>
              </div>
              <div className={styles.step}>
                <span className={styles.stepNumber}>2</span>
                <p>推出"琼斯拥有福特车，或者布朗在巴塞罗那"</p>
              </div>
              <div className={styles.step}>
                <span className={styles.stepNumber}>3</span>
                <p>实际上：琼斯不拥有福特车（租的），但布朗确实在巴塞罗那</p>
              </div>
              <div className={styles.step}>
                <span className={styles.stepNumber}>4</span>
                <p>结果：信念是真的、被证成的，但带有运气成分，不是真正的知识</p>
              </div>
            </div>
          </div>
          <div className={styles.gettierImplication}>
            <h4>对AI的启示</h4>
            <p>AI系统可能产生偶然正确的输出，用户可能相信并认为有"证成"，但这可能是新的盖梯尔案例。</p>
          </div>
        </div>
      </div>
    </section>
  );
}

// 理性主义vs经验主义幻灯片
function RationalismEmpiricismSlide({ isActive }) {
  return (
    <section className={`${styles.slide} ${isActive ? styles.active : ''}`}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          主要认识论传统
        </Heading>
        <div className={styles.traditionsContainer}>
          <div className={styles.tradition}>
            <h3>理性主义 (Rationalism)</h3>
            <div className={styles.traditionContent}>
              <h4>核心观点</h4>
              <ul>
                <li>理性是知识的主要来源</li>
                <li>强调独立思考和逻辑推理</li>
                <li>某些知识可以是先验的</li>
              </ul>
              <h4>代表人物</h4>
              <p>笛卡尔、斯宾诺莎、莱布尼茨</p>
              <h4>AI对应</h4>
              <p>符号主义AI - 强调逻辑规则和演绎推理</p>
            </div>
          </div>
          <div className={styles.tradition}>
            <h3>经验主义 (Empiricism)</h3>
            <div className={styles.traditionContent}>
              <h4>核心观点</h4>
              <ul>
                <li>感觉经验是知识的主导来源</li>
                <li>心灵如"白板"，知识来自后天经验</li>
                <li>强调观察和实验</li>
              </ul>
              <h4>代表人物</h4>
              <p>洛克、贝克莱、休谟</p>
              <h4>AI对应</h4>
              <p>连接主义AI - 强调从数据中学习模式</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

// AI定义幻灯片
function AIDefinitionSlide({ isActive }) {
  return (
    <section className={`${styles.slide} ${isActive ? styles.active : ''}`}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          II. 人工智能：范式与知识处理
        </Heading>
        <div className={styles.aiDefinitionContainer}>
          <div className={styles.definitionCard}>
            <h3>人工智能的定义</h3>
            <p>致力于构建能够展现智能行为的人造物的领域，涵盖推理、知识表示、规划、学习、自然语言处理、感知和机器人技术等广泛子领域。</p>
          </div>
          <div className={styles.aiGoals}>
            <h3>核心目标与能力</h3>
            <div className={styles.goalsGrid}>
              <div className={styles.goalItem}>
                <div className={styles.goalIcon}>🎯</div>
                <h4>通用人工智能(AGI)</h4>
                <p>能够像人类一样完成任何智力任务</p>
              </div>
              <div className={styles.goalItem}>
                <div className={styles.goalIcon}>🧠</div>
                <h4>演绎推理</h4>
                <p>逐步的逻辑推理和问题解决</p>
              </div>
              <div className={styles.goalItem}>
                <div className={styles.goalIcon}>📚</div>
                <h4>知识表示</h4>
                <p>存储知识并按规则推理演绎</p>
              </div>
            </div>
          </div>
          <div className={styles.aiDuality}>
            <h3>AI的双重性</h3>
            <div className={styles.dualityComparison}>
              <div className={styles.dualityItem}>
                <strong>实用工具</strong>
                <p>解决实际问题：智能助理、推荐系统、自动驾驶</p>
              </div>
              <div className={styles.dualityItem}>
                <strong>科学探索</strong>
                <p>模拟、理解甚至复制人类智能</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

// 符号主义vs连接主义幻灯片
function AIParadigmsSlide({ isActive }) {
  return (
    <section className={`${styles.slide} ${isActive ? styles.active : ''}`}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          主流AI范式及其认识论预设
        </Heading>
        <div className={styles.paradigmsContainer}>
          <div className={styles.paradigm}>
            <h3>符号主义AI (逻辑为本)</h3>
            <div className={styles.paradigmContent}>
              <h4>核心思想</h4>
              <ul>
                <li>通过符号操纵实现智能行为</li>
                <li>显式知识表示和逻辑推理</li>
                <li>基于公理和逻辑体系</li>
              </ul>
              <h4>认识论特征</h4>
              <ul>
                <li>强调先验知识和逻辑演绎</li>
                <li>知识表示明确，推理透明</li>
                <li>对应理性主义哲学传统</li>
              </ul>
              <h4>局限性</h4>
              <ul>
                <li>难以处理模糊、不确定信息</li>
                <li>符号接地问题</li>
                <li>知识获取瓶颈</li>
              </ul>
            </div>
          </div>
          <div className={styles.paradigm}>
            <h3>连接主义AI (神经网络)</h3>
            <div className={styles.paradigmContent}>
              <h4>核心思想</h4>
              <ul>
                <li>模仿大脑神经元连接机制</li>
                <li>从大规模数据中学习模式</li>
                <li>隐式分布式知识表示</li>
              </ul>
              <h4>认识论特征</h4>
              <ul>
                <li>强调从"经验"(数据)中学习</li>
                <li>知识是概率性和情境化的</li>
                <li>对应经验主义哲学传统</li>
              </ul>
              <h4>局限性</h4>
              <ul>
                <li>"黑箱"问题，缺乏透明性</li>
                <li>极度依赖训练数据质量</li>
                <li>可能学习和放大数据偏见</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

// AI对JTB的挑战幻灯片
function AIChallengeJTBSlide({ isActive }) {
  return (
    <section className={`${styles.slide} ${isActive ? styles.active : ''}`}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          III. AI对传统认识论的冲击
        </Heading>
        <div className={styles.challengeContainer}>
          <div className={styles.challengeQuestion}>
            <h3>AI能否"拥有知识"？</h3>
            <p>将JTB框架应用于AI系统面临根本性困难</p>
          </div>
          <div className={styles.jtbChallenges}>
            <div className={styles.challengeItem}>
              <div className={styles.challengeIcon}>B</div>
              <h4>信念 (Belief) 挑战</h4>
              <ul>
                <li>AI缺乏意向性和意识</li>
                <li>输出基于算法，非主观"相信"</li>
                <li>不以说真话或欺骗为目标</li>
              </ul>
            </div>
            <div className={styles.challengeItem}>
              <div className={styles.challengeIcon}>T</div>
              <h4>真理 (Truth) 挑战</h4>
              <ul>
                <li>基于统计模式，非客观实在</li>
                <li>"统计真理"vs"符合论真理"</li>
                <li>可能产生"幻觉"(虚假信息)</li>
              </ul>
            </div>
            <div className={styles.challengeItem}>
              <div className={styles.challengeIcon}>J</div>
              <h4>证成 (Justification) 挑战</h4>
              <ul>
                <li>内部运作不透明("黑箱")</li>
                <li>缺乏意向性和责任感</li>
                <li>仅基于统计可能性</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

// 大型语言模型幻灯片
function LLMSlide({ isActive }) {
  return (
    <section className={`${styles.slide} ${isActive ? styles.active : ''}`}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          大型语言模型：随机鹦鹉还是理解的萌芽？
        </Heading>
        <div className={styles.llmContainer}>
          <div className={styles.llmDebate}>
            <div className={styles.debatePosition}>
              <h3>"随机鹦鹉"观点</h3>
              <ul>
                <li>通过预测下一个词元模仿语言</li>
                <li>统计模式匹配，缺乏真正理解</li>
                <li>句法成功，非语义理解</li>
                <li>缺乏意向状态的深层网络</li>
              </ul>
            </div>
            <div className={styles.debatePosition}>
              <h3>"理解萌芽"观点</h3>
              <ul>
                <li>通过分布式语义捕捉意义</li>
                <li>学习推论网络中的词语角色</li>
                <li>多模态模型的符号接地</li>
                <li>可能展现某种"类理解"行为</li>
              </ul>
            </div>
          </div>
          <div className={styles.llmRisk}>
            <h3>⚠️ "理解的幻觉"风险</h3>
            <p>LLM的高度自然输出容易造成用户的拟人化偏见，过度归因其知识、可靠性和意图，导致不加批判地接受其输出。</p>
          </div>
          <div className={styles.philosophicalQuestion}>
            <h3>🤔 深层哲学问题</h3>
            <p>这场辩论触及"意义"和"理解"本身的定义。如果采用功能主义立场，LLM可能展现某种"类理解"；如果坚持意识和意向性要求，则LLM显然不具备理解能力。</p>
          </div>
        </div>
      </div>
    </section>
  );
}

// 中文屋论证幻灯片
function ChineseRoomSlide({ isActive }) {
  return (
    <section className={`${styles.slide} ${isActive ? styles.active : ''}`}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          "中文屋论证"及其对现代AI的意义
        </Heading>
        <div className={styles.chineseRoomContainer}>
          <div className={styles.argumentDescription}>
            <h3>塞尔的思想实验 (1980)</h3>
            <div className={styles.experimentSteps}>
              <div className={styles.experimentStep}>
                <span className={styles.stepIcon}>🏠</span>
                <p>不懂中文的人被关在房间里</p>
              </div>
              <div className={styles.experimentStep}>
                <span className={styles.stepIcon}>📖</span>
                <p>房间有英文规则手册和中文符号库</p>
              </div>
              <div className={styles.experimentStep}>
                <span className={styles.stepIcon}>❓</span>
                <p>外面的人递进中文问题</p>
              </div>
              <div className={styles.experimentStep}>
                <span className={styles.stepIcon}>🔍</span>
                <p>房间里的人按规则匹配符号</p>
              </div>
              <div className={styles.experimentStep}>
                <span className={styles.stepIcon}>✅</span>
                <p>递出正确的中文答案</p>
              </div>
            </div>
          </div>
          <div className={styles.argumentCore}>
            <h3>核心论点</h3>
            <p>尽管系统能通过图灵测试，但房间里的人从未理解中文。他只是在进行<strong>符号操纵(句法)</strong>，而没有掌握<strong>符号意义(语义)</strong>。</p>
          </div>
          <div className={styles.modernRelevance}>
            <h3>对现代AI的意义</h3>
            <div className={styles.relevancePoints}>
              <div className={styles.relevancePoint}>
                <h4>LLM的相似性</h4>
                <p>LLM处理文本序列，基于统计规律生成输出，类似中文屋的符号操纵</p>
              </div>
              <div className={styles.relevancePoint}>
                <h4>系统答辩</h4>
                <p>整个AI系统(包括算法、数据、环境)作为整体可能展现理解</p>
              </div>
              <div className={styles.relevancePoint}>
                <h4>持续警示</h4>
                <p>提醒我们区分外部行为表现与内在理解和意识</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

// 主要组件导出
export default function EpistemologyAI() {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isFullscreen, setIsFullscreen] = useState(false);

// 黑箱问题幻灯片
function BlackBoxSlide({ isActive }) {
  return (
    <section className={`${styles.slide} ${isActive ? styles.active : ''}`}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          IV. 驾驭高级AI的认知挑战
        </Heading>
        <div className={styles.blackBoxContainer}>
          <div className={styles.problemDescription}>
            <h3>"黑箱"问题：不透明性、信任与认知责任</h3>
            <p>现代AI，特别是深度学习模型，其内部运作机制极其复杂，即使设计者也难以完全理解其决策过程。</p>
          </div>
          <div className={styles.blackBoxChallenges}>
            <div className={styles.challengeCard}>
              <h4>🔒 证成挑战</h4>
              <p>如果证成要求理解信念为真的理由，那么黑箱AI的输出难以被证成，即使结果"正确"。</p>
            </div>
            <div className={styles.challengeCard}>
              <h4>⚖️ 责任归属</h4>
              <p>当AI做出错误决策时，责任应归咎于AI、设计者、部署者还是用户？责任模糊化削弱了知识可靠性。</p>
            </div>
            <div className={styles.challengeCard}>
              <h4>🤔 证成重新定义</h4>
              <p>是否应转向基于性能和可靠性的证成观？还是坚持要求通达理由的传统证成？</p>
            </div>
          </div>
          <div className={styles.solutionDirection}>
            <h4>解决方向</h4>
            <ul>
              <li>发展可解释AI (XAI) 技术</li>
              <li>神经符号AI混合方法</li>
              <li>建立新的认知信任框架</li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
}

// 算法偏见幻灯片
function AlgorithmicBiasSlide({ isActive }) {
  return (
    <section className={`${styles.slide} ${isActive ? styles.active : ''}`}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          算法偏见作为认识论失败
        </Heading>
        <div className={styles.biasContainer}>
          <div className={styles.biasDefinition}>
            <h3>认识论视角下的算法偏见</h3>
            <p>算法偏见不仅是伦理问题，更是一种系统性的<strong>认识论失败</strong>——基于有缺陷的认知基础形成扭曲的"知识"。</p>
          </div>
          <div className={styles.biasProcess}>
            <h4>偏见产生机制</h4>
            <div className={styles.processSteps}>
              <div className={styles.processStep}>
                <span className={styles.stepNumber}>1</span>
                <div>
                  <h5>不确定证据</h5>
                  <p>训练数据包含历史偏见和刻板印象</p>
                </div>
              </div>
              <div className={styles.processStep}>
                <span className={styles.stepNumber}>2</span>
                <div>
                  <h5>错误学习</h5>
                  <p>AI系统学习并内化这些有缺陷的模式</p>
                </div>
              </div>
              <div className={styles.processStep}>
                <span className={styles.stepNumber}>3</span>
                <div>
                  <h5>扭曲表征</h5>
                  <p>形成关于世界的错误"知识"表征</p>
                </div>
              </div>
              <div className={styles.processStep}>
                <span className={styles.stepNumber}>4</span>
                <div>
                  <h5>固化放大</h5>
                  <p>在应用中进一步固化和放大偏见</p>
                </div>
              </div>
            </div>
          </div>
          <div className={styles.epistemicInjustice}>
            <h4>认知不正义 (Epistemic Injustice)</h4>
            <p>AI系统由于偏见而系统性地贬低、忽视或错误表征特定群体，损害了他们作为认知主体的地位和尊严。</p>
          </div>
          <div className={styles.biasResolution}>
            <h4>解决策略</h4>
            <ul>
              <li>确保数据和算法提供公正、全面的认知基础</li>
              <li>识别和纠正AI认知过程中的系统性"盲点"</li>
              <li>在AI全生命周期贯彻认知审慎和认知公正原则</li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
}

// AI幻觉幻灯片
function AIHallucinationSlide({ isActive }) {
  return (
    <section className={`${styles.slide} ${isActive ? styles.active : ''}`}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          AI"幻觉"：错误信息与真理的挑战
        </Heading>
        <div className={styles.hallucinationContainer}>
          <div className={styles.hallucinationDefinition}>
            <h3>什么是AI"幻觉"？</h3>
            <p>AI系统产生看似连贯、有说服力，但实际上是虚假的、捏造的或与输入不符的内容。</p>
          </div>
          <div className={styles.hallucinationCauses}>
            <h4>产生机制</h4>
            <div className={styles.causesGrid}>
              <div className={styles.causeItem}>
                <span className={styles.causeIcon}>🎲</span>
                <h5>概率特性</h5>
                <p>生成统计上最可能的序列，而非绝对真实内容</p>
              </div>
              <div className={styles.causeItem}>
                <span className={styles.causeIcon}>📊</span>
                <h5>数据问题</h5>
                <p>训练数据中的错误、矛盾或过时信息</p>
              </div>
              <div className={styles.causeItem}>
                <span className={styles.causeIcon}>❓</span>
                <h5>边界无知</h5>
                <p>对自身知识边界缺乏认知，在不确定领域强行作答</p>
              </div>
              <div className={styles.causeItem}>
                <span className={styles.causeIcon}>🔄</span>
                <h5>雪崩效应</h5>
                <p>为维持连贯性，基于错误内容继续生成更多错误</p>
              </div>
            </div>
          </div>
          <div className={styles.epistemicThreat}>
            <h3>对知识"真理"条件的威胁</h3>
            <p>AI幻觉的独特之处在于它是在"没有人类欺骗意图"的情况下产生的错误信息，其高度拟人化和貌似合理性使用户容易信以为真。</p>
          </div>
          <div className={styles.verificationImportance}>
            <h4>验证的极端重要性</h4>
            <ul>
              <li>用户不能简单地将AI生成内容视为权威或事实</li>
              <li>必须培养批判性审视和独立核查的习惯</li>
              <li>需要新的工具、方法和素养来辨别AI错误信息</li>
              <li>确保依赖的"知识"是真实和可靠的</li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
}

// 神经符号AI幻灯片
function NeuroSymbolicSlide({ isActive }) {
  return (
    <section className={`${styles.slide} ${isActive ? styles.active : ''}`}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          V. AI时代知识的重构：未来轨迹
        </Heading>
        <div className={styles.neuroSymbolicContainer}>
          <div className={styles.integrationVision}>
            <h3>神经符号AI：迈向更整合与鲁棒的认知架构</h3>
            <p>融合神经网络的学习能力与符号系统的逻辑推理能力，构建更可靠、可解释且认知能力更全面的AI系统。</p>
          </div>
          <div className={styles.integrationApproaches}>
            <h4>整合方式</h4>
            <div className={styles.approachesGrid}>
              <div className={styles.approachItem}>
                <h5>🧠→🔣 神经到符号</h5>
                <p>神经网络处理感知输入，符号引擎进行高层推理</p>
              </div>
              <div className={styles.approachItem}>
                <h5>🔣→🧠 符号到神经</h5>
                <p>符号知识指导神经网络学习过程</p>
              </div>
              <div className={styles.approachItem}>
                <h5>🔄 混合架构</h5>
                <p>神经和符号组件深度集成，相互增强</p>
              </div>
            </div>
          </div>
          <div className={styles.epistemicAdvantages}>
            <h4>认识论优势</h4>
            <div className={styles.advantagesGrid}>
              <div className={styles.advantageItem}>
                <span className={styles.advantageIcon}>🔍</span>
                <h5>增强透明度</h5>
                <p>符号组件提供可解释的推理过程</p>
              </div>
              <div className={styles.advantageItem}>
                <span className={styles.advantageIcon}>⚡</span>
                <h5>提升效率</h5>
                <p>神经网络弥补符号系统的学习不足</p>
              </div>
              <div className={styles.advantageItem}>
                <span className={styles.advantageIcon}>🎯</span>
                <h5>真正推理</h5>
                <p>超越统计推断，实现基于规则的演绎推理</p>
              </div>
              <div className={styles.advantageItem}>
                <span className={styles.advantageIcon}>🛡️</span>
                <h5>认知鲁棒</h5>
                <p>更坚实的"证成"和可信的推理链条</p>
              </div>
            </div>
          </div>
          <div className={styles.philosophicalSignificance}>
            <h4>哲学意义</h4>
            <p>神经符号AI可被视为在计算层面实现理性主义和经验主义两种认识论路径整合的尝试，有望克服单一范式的局限。</p>
          </div>
        </div>
      </div>
    </section>
  );
}

// 人机认知增强幻灯片
function HumanAIAugmentationSlide({ isActive }) {
  return (
    <section className={`${styles.slide} ${isActive ? styles.active : ''}`}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          人机认知增强："扩展心智"与"认知卸载"
        </Heading>
        <div className={styles.augmentationContainer}>
          <div className={styles.extendedMind}>
            <h3>扩展心智理论 (Extended Mind Theory)</h3>
            <p>人类认知过程可以延伸到外部世界，利用环境中的工具和资源来辅助或构成认知活动。AI正成为这种"认知工具"的强大形态。</p>
          </div>
          <div className={styles.cognitiveOffloading}>
            <h3>认知卸载 (Cognitive Offloading)</h3>
            <div className={styles.offloadingExamples}>
              <div className={styles.offloadingItem}>
                <span className={styles.offloadingIcon}>🧠</span>
                <h4>记忆卸载</h4>
                <p>将信息存储转移到AI助手</p>
              </div>
              <div className={styles.offloadingItem}>
                <span className={styles.offloadingIcon}>🔢</span>
                <h4>计算卸载</h4>
                <p>复杂运算和数据分析</p>
              </div>
              <div className={styles.offloadingItem}>
                <span className={styles.offloadingIcon}>🔍</span>
                <h4>检索卸载</h4>
                <p>信息查找和文献综述</p>
              </div>
              <div className={styles.offloadingItem}>
                <span className={styles.offloadingIcon}>⚖️</span>
                <h4>决策卸载</h4>
                <p>辅助判断和选择</p>
              </div>
            </div>
          </div>
          <div className={styles.epistemicChallenges}>
            <h3>新的认识论挑战</h3>
            <div className={styles.challengesGrid}>
              <div className={styles.challengeBox}>
                <h4>🤔 认知主体边界</h4>
                <p>如果知识分布在人机混合系统中，"谁是知者？"、"知识归属于谁？"</p>
              </div>
              <div className={styles.challengeBox}>
                <h4>⚠️ 认知依赖风险</h4>
                <p>过度依赖AI可能导致人类自身认知能力退化或"去技能化"</p>
              </div>
              <div className={styles.challengeBox}>
                <h4>⚖️ 责任分配</h4>
                <p>在分布式认知中如何分配认知责任？</p>
              </div>
            </div>
          </div>
          <div className={styles.balanceStrategy}>
            <h4>平衡策略</h4>
            <p>如何在利用AI增强认知能力的同时，避免认知过度依赖和核心技能丧失？需要培养具备高度AI素养和认知自主性的人类。</p>
          </div>
        </div>
      </div>
    </section>
  );
}

// 科学发现与教育转向幻灯片
function ScienceEducationSlide({ isActive }) {
  return (
    <section className={`${styles.slide} ${isActive ? styles.active : ''}`}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          AI驱动下科学发现与教育的认识论转向
        </Heading>
        <div className={styles.scienceEducationContainer}>
          <div className={styles.scienceSection}>
            <h3>🔬 科学发现领域</h3>
            <div className={styles.scienceContent}>
              <div className={styles.scienceItem}>
                <h4>代理式AI (Agentic AI)</h4>
                <p>具备推理、规划和自主决策能力，改变科学家进行研究的方式</p>
                <ul>
                  <li>自动化文献回顾</li>
                  <li>生成原创性假设</li>
                  <li>设计实验方案</li>
                  <li>分析复杂数据</li>
                </ul>
              </div>
              <div className={styles.scienceChallenge}>
                <h4>⚠️ 认识论挑战</h4>
                <ul>
                  <li>如何信任AI生成的科学假设？</li>
                  <li>科学可重复性如何保障？</li>
                  <li>需要新的科学方法论和验证标准</li>
                  <li>确保AI贡献在认识论上稳固可信</li>
                </ul>
              </div>
            </div>
          </div>
          <div className={styles.educationSection}>
            <h3>🎓 教育领域</h3>
            <div className={styles.educationContent}>
              <div className={styles.educationItem}>
                <h4>生成式AI (GenAI) 变革</h4>
                <ul>
                  <li>个性化学习路径</li>
                  <li>智能辅导系统</li>
                  <li>即时反馈机制</li>
                  <li>定制化教学内容</li>
                </ul>
              </div>
              <div className={styles.educationChallenge}>
                <h4>⚠️ 根本性关切</h4>
                <ul>
                  <li>过度关注劳动力需求可能导致肤浅学习</li>
                  <li>AI可能强化现有教育不平等</li>
                  <li>学生主体性和能动性需要保护</li>
                  <li>批判性思维培养面临挑战</li>
                </ul>
              </div>
            </div>
          </div>
          <div className={styles.newFoundation}>
            <h3>新的本体-认识论基础</h3>
            <p>教育重心需要从传统知识传递模式转向培养学生与AI协同学习、批判性评估AI信息、以及在AI辅助下进行创新性探究的能力。</p>
          </div>
        </div>
      </div>
    </section>
  );
}

// 总结反思幻灯片
function SummarySlide({ isActive }) {
  return (
    <section className={`${styles.slide} ${isActive ? styles.active : ''}`}>
      <div className={styles.slideContent}>
        <Heading as="h2" className={styles.slideTitle}>
          总结性反思：知识与探究图景的演化
        </Heading>
        <div className={styles.summaryContainer}>
          <div className={styles.keyInsights}>
            <h3>核心洞察</h3>
            <div className={styles.insightsGrid}>
              <div className={styles.insightItem}>
                <span className={styles.insightIcon}>🔍</span>
                <h4>概念重构</h4>
                <p>传统JTB框架在AI时代面临根本挑战，需要重新定义"信念"、"真理"和"证成"</p>
              </div>
              <div className={styles.insightItem}>
                <span className={styles.insightIcon}>⚖️</span>
                <h4>范式融合</h4>
                <p>神经符号AI代表理性主义与经验主义在计算层面的整合尝试</p>
              </div>
              <div className={styles.insightItem}>
                <span className={styles.insightIcon}>🤝</span>
                <h4>人机协同</h4>
                <p>扩展心智理论为理解人机认知增强提供新视角</p>
              </div>
              <div className={styles.insightItem}>
                <span className={styles.insightIcon}>⚠️</span>
                <h4>认知风险</h4>
                <p>算法偏见、AI幻觉和认知依赖构成新的认识论挑战</p>
              </div>
            </div>
          </div>
          <div className={styles.futureDirections}>
            <h3>未来方向</h3>
            <div className={styles.directionsGrid}>
              <div className={styles.directionItem}>
                <h4>🔬 理论发展</h4>
                <ul>
                  <li>发展适应AI时代的新认识论框架</li>
                  <li>重新定义知识、理解和证成概念</li>
                  <li>探索分布式认知的哲学基础</li>
                </ul>
              </div>
              <div className={styles.directionItem}>
                <h4>🛠️ 技术进步</h4>
                <ul>
                  <li>推进可解释AI和神经符号AI</li>
                  <li>开发认知增强技术</li>
                  <li>建立AI安全和对齐机制</li>
                </ul>
              </div>
              <div className={styles.directionItem}>
                <h4>📚 教育改革</h4>
                <ul>
                  <li>培养AI素养和批判性思维</li>
                  <li>重新设计教育目标和方法</li>
                  <li>平衡人机协作与人类自主性</li>
                </ul>
              </div>
              <div className={styles.directionItem}>
                <h4>⚖️ 伦理治理</h4>
                <ul>
                  <li>建立AI认识论责任框架</li>
                  <li>防范认知不正义</li>
                  <li>促进认知公平和包容</li>
                </ul>
              </div>
            </div>
          </div>
          <div className={styles.finalThought}>
            <h3>最终思考</h3>
            <p>人工智能不仅是技术革命，更是认识论革命。我们正站在重新定义"知识"本质的历史节点上。如何在拥抱AI带来的认知增强的同时，保持人类理性的独立性和批判性，将是这个时代最重要的哲学课题。</p>
          </div>
        </div>
      </div>
    </section>
  );
}

// 致谢幻灯片
function ThankYouSlide({ isActive }) {
  return (
    <section className={`${styles.slide} ${isActive ? styles.active : ''}`}>
      <div className={styles.slideContent}>
        <Heading as="h1" className={styles.thankYouTitle}>
          谢谢聆听
        </Heading>
        <div className={styles.thankYouContent}>
          <p className={styles.thankYouSubtitle}>
            认识论、知识论与人工智能的交织
          </p>
          <div className={styles.contactInfo}>
            <h3>讨论与交流</h3>
            <p>欢迎就以下话题进行深入探讨：</p>
            <ul>
              <li>AI时代的知识定义</li>
              <li>人机认知协同的哲学基础</li>
              <li>认识论框架的重构方向</li>
              <li>教育和科研的变革路径</li>
            </ul>
          </div>
          <div className={styles.decorativeElements}>
            <div className={styles.circle}></div>
            <div className={styles.triangle}></div>
            <div className={styles.square}></div>
          </div>
        </div>
      </div>
    </section>
  );
}

  // 定义所有幻灯片
  const slides = [
    CoverSlide,
    OutlineSlide,
    KnowledgeFoundationSlide,
    JTBSlide,
    GettierSlide,
    RationalismEmpiricismSlide,
    AIDefinitionSlide,
    AIParadigmsSlide,
    AIChallengeJTBSlide,
    LLMSlide,
    ChineseRoomSlide,
    BlackBoxSlide,
    AlgorithmicBiasSlide,
    AIHallucinationSlide,
    NeuroSymbolicSlide,
    HumanAIAugmentationSlide,
    ScienceEducationSlide,
    SummarySlide,
    ThankYouSlide,
  ];

  // 键盘导航
  useEffect(() => {
    const handleKeyPress = (event) => {
      if (event.key === 'ArrowRight' || event.key === ' ') {
        setCurrentSlide(prev => Math.min(slides.length - 1, prev + 1));
      } else if (event.key === 'ArrowLeft') {
        setCurrentSlide(prev => Math.max(0, prev - 1));
      } else if (event.key === 'p' || event.key === 'P') {
        toggleFullscreen();
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [slides.length]);

  // 全屏切换
  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  return (
    <Layout
      title="认识论、知识论与人工智能的交织"
      description="探讨认识论、知识论与人工智能的深度交织关系"
    >
      <div className={`${styles.presentationContainer} ${isFullscreen ? styles.fullscreen : ''}`}>
        <div className={styles.slideContainer}>
          {slides.map((SlideComponent, index) => (
            <SlideComponent key={index} isActive={index === currentSlide} />
          ))}
        </div>
        
        <div className={styles.navigation}>
          <button 
            onClick={() => setCurrentSlide(prev => Math.max(0, prev - 1))}
            disabled={currentSlide === 0}
            className={styles.navButton}
          >
            ← 上一页
          </button>
          <span className={styles.slideCounter}>
            {currentSlide + 1} / {slides.length}
          </span>
          <button 
            onClick={() => setCurrentSlide(prev => Math.min(slides.length - 1, prev + 1))}
            disabled={currentSlide === slides.length - 1}
            className={styles.navButton}
          >
            下一页 →
          </button>
        </div>

        <div className={styles.controls}>
          <button onClick={toggleFullscreen} className={styles.fullscreenButton}>
            {isFullscreen ? '退出全屏' : '全屏 (P)'}
          </button>
        </div>

        <div className={styles.instructions}>
          <p>使用 ← → 键或空格键导航，按 P 键全屏</p>
        </div>
      </div>
    </Layout>
  );
}
